package com.caidaocloud.open.auth.service.interfaces.facade;

import javax.servlet.http.HttpServletRequest;

import com.caidaocloud.hrpaas.metadata.sdk.util.ResultUtil;
import com.caidaocloud.open.auth.service.infrastructure.util.RequestUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
@Slf4j
// @RestController
@RequestMapping("/api/open/v1/test")

public class TestController {

	@RequestMapping("hello")
	public Result<String> hello(HttpServletRequest request){
		return Result.ok(FastjsonUtil.toJson(RequestUtil.getRequestHeaders(request)));
	}

}
