package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;

import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

@Component
public class OAuthClientExceptionEntryPoint extends OAuth2AuthenticationEntryPoint {
    public OAuthClientExceptionEntryPoint(OAuthExceptionTranslator oAuthExceptionTranslator) {
        super();
        setExceptionTranslator(oAuthExceptionTranslator);
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
        response.setStatus(400);
        response.setContentType("application/json;charset=utf-8");

        PrintWriter out = response.getWriter();
        out.println(FastjsonUtil.toJson(formatException(e)));
        out.flush();
        out.close();
    }

    public Result formatException(AuthenticationException e) {
        if (e instanceof BadCredentialsException || e instanceof InternalAuthenticationServiceException) {
          return   Result.fail(20002,"The client secret is invalid.");
        }
        return Result.fail(e.getMessage());
    }
}
