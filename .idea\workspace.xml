<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="68c38bb3-e8dd-42d5-bc8a-ea15818f8550" name="Changes" comment="[fix] request fail">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/AliyunKmsJwtAccessTokenConverter.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/AliyunKmsJwtAccessTokenConverter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/oauth/WebSecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/WebSecurityConfig.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="CodeStyleSettingsInfer">
    <option name="done" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="dev" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/client/ClientCredentialsTokenGranter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-tx/5.1.2.RELEASE/spring-tx-5.1.2.RELEASE-sources.jar!/org/springframework/transaction/interceptor/TransactionAspectSupport.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-aop/5.1.2.RELEASE/spring-aop-5.1.2.RELEASE-sources.jar!/org/springframework/aop/framework/ReflectiveMethodInvocation.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/DefaultTokenServices.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-core/5.1.2.RELEASE/spring-core-5.1.2.RELEASE-sources.jar!/org/springframework/cglib/proxy/MethodProxy.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-config/5.1.1.RELEASE/spring-security-config-5.1.1.RELEASE-sources.jar!/org/springframework/security/config/annotation/web/configuration/WebSecurityConfigurerAdapter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE.jar!/org/springframework/security/oauth2/config/annotation/web/configurers/AuthorizationServerEndpointsConfigurer.class" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-core/5.1.1.RELEASE/spring-security-core-5.1.1.RELEASE-sources.jar!/org/springframework/security/authentication/ProviderManager.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/CompositeTokenGranter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/TokenGranter.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/KmsJwtSigner.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/config/annotation/web/configurers/AuthorizationServerEndpointsConfigurer.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-core/5.1.1.RELEASE/spring-security-core-5.1.1.RELEASE-sources.jar!/org/springframework/security/authentication/dao/DaoAuthenticationProvider.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-core/5.1.1.RELEASE/spring-security-core-5.1.1.RELEASE-sources.jar!/org/springframework/security/authentication/dao/AbstractUserDetailsAuthenticationProvider.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="C:\mvn\setting_caidao\settings-new.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectId" id="31UpGRhXFMaDwrJPX5ZzqpqfCoG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/src/main/resources" />
    <property name="project.structure.last.edited" value="Project" />
    <property name="project.structure.proportion" value="0.0" />
    <property name="project.structure.side.proportion" value="0.36091954" />
    <property name="settings.editor.selected.configurable" value="MavenSettings" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="ReaderModeSettings">
    <option name="enabled" value="false" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\caidao\caidao_open_auth_service\src\main\resources" />
      <recent name="C:\caidao\caidao_open_auth_service\src\test\resources" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.caidaocloud.open.auth.service.infrastructure.config" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.caidaocloud.open.auth.service.infrastructure.config.oauth" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.CaidaoAuthApplication (1)">
    <configuration default="true" type="DjangoTestsConfigurationType">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="TARGET" value="" />
      <option name="SETTINGS_FILE" value="" />
      <option name="CUSTOM_SETTINGS" value="false" />
      <option name="USE_OPTIONS" value="false" />
      <option name="OPTIONS" value="" />
      <method v="2" />
    </configuration>
    <configuration name="KmsJwtSignerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="caidao-open-auth-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun" />
      <option name="MAIN_CLASS_NAME" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSignerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KmsJwtSignerTest.testDevConfigurationConstants" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="caidao-open-auth-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun" />
      <option name="MAIN_CLASS_NAME" value="com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSignerTest" />
      <option name="METHOD_NAME" value="testDevConfigurationConstants" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SHA256UtilTest.encode_test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="caidao-open-auth-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.caidaocloud.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.caidaocloud.util" />
      <option name="MAIN_CLASS_NAME" value="com.caidaocloud.util.SHA256UtilTest" />
      <option name="METHOD_NAME" value="encode_test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="PyBehaveRunConfigurationType" factoryName="Behave">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="ADDITIONAL_ARGS" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="CaidaoAuthApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true">
      <module name="caidao-open-auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.caidaocloud.open.auth.service.OpenAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CaidaoAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="caidao-open-auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.caidao.auth.CaidaoAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.CaidaoAuthApplication (1)" />
        <item itemvalue="JUnit.SHA256UtilTest.encode_test" />
        <item itemvalue="JUnit.KmsJwtSignerTest.testDevConfigurationConstants" />
        <item itemvalue="JUnit.KmsJwtSignerTest" />
        <item itemvalue="Spring Boot.CaidaoAuthApplication (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="68c38bb3-e8dd-42d5-bc8a-ea15818f8550" name="Changes" comment="" />
      <created>1755588414410</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755588414410</updated>
      <workItem from="1755588417337" duration="7280000" />
      <workItem from="1756102526750" duration="10760000" />
      <workItem from="1756177163520" duration="13335000" />
      <workItem from="1756360677547" duration="12611000" />
      <workItem from="1756450531295" duration="9201000" />
      <workItem from="1756561171533" duration="4426000" />
      <workItem from="1756692969211" duration="13421000" />
      <workItem from="1756779676014" duration="7696000" />
      <workItem from="1756864848655" duration="7394000" />
      <workItem from="1756950656497" duration="1123000" />
      <workItem from="1756952237084" duration="17152000" />
      <workItem from="1757040043346" duration="5737000" />
      <workItem from="1757059013253" duration="4835000" />
      <workItem from="1757312918817" duration="5066000" />
      <workItem from="1757570762220" duration="1248000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <created>1755589546195</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755589546196</updated>
    </task>
    <task id="LOCAL-00002" summary="项目初始化">
      <created>1755589691263</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755589691263</updated>
    </task>
    <task id="LOCAL-00003" summary="项目初始化">
      <created>1755590547219</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755590547219</updated>
    </task>
    <task id="LOCAL-00004" summary="项目初始化">
      <created>1755600229680</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755600229680</updated>
    </task>
    <task id="LOCAL-00005" summary="项目初始化">
      <created>1756179674365</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1756179674365</updated>
    </task>
    <task id="LOCAL-00006" summary="项目初始化">
      <created>1756207494283</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1756207494284</updated>
    </task>
    <task id="LOCAL-00007" summary="项目初始化">
      <created>1756366998557</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1756366998557</updated>
    </task>
    <task id="LOCAL-00008" summary="项目初始化">
      <created>1756370506831</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1756370506831</updated>
    </task>
    <task id="LOCAL-00009" summary="项目初始化">
      <created>1756379666324</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1756379666324</updated>
    </task>
    <task id="LOCAL-00010" summary="项目初始化">
      <created>1756717123793</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1756717123793</updated>
    </task>
    <task id="LOCAL-00011" summary="项目初始化">
      <created>1756780841432</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1756780841432</updated>
    </task>
    <task id="LOCAL-00012" summary="项目初始化">
      <created>1756812292005</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1756812292005</updated>
    </task>
    <task id="LOCAL-00013" summary="项目初始化">
      <created>1756889687424</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1756889687424</updated>
    </task>
    <task id="LOCAL-00014" summary="项目初始化">
      <created>1756895813116</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1756895813117</updated>
    </task>
    <task id="LOCAL-00015" summary="项目初始化">
      <created>1756964861316</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1756964861316</updated>
    </task>
    <task id="LOCAL-00016" summary="sha256签名">
      <created>1756967178901</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1756967178901</updated>
    </task>
    <task id="LOCAL-00017" summary="sha256签名">
      <created>1756967362671</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1756967362680</updated>
    </task>
    <task id="LOCAL-00018" summary="项目初始化">
      <created>1756969933620</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1756969933620</updated>
    </task>
    <task id="LOCAL-00019" summary="项目初始化">
      <created>1756970995996</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1756970995997</updated>
    </task>
    <task id="LOCAL-00020" summary="sha256签名">
      <created>1756975830152</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1756975830152</updated>
    </task>
    <task id="LOCAL-00021" summary="测试接口">
      <created>1756976033261</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1756976033261</updated>
    </task>
    <task id="LOCAL-00022" summary="测试yml">
      <created>1756982329843</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1756982329844</updated>
    </task>
    <task id="LOCAL-00023" summary="项目初始化">
      <created>1756982773115</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1756982773116</updated>
    </task>
    <task id="LOCAL-00024" summary="aliyun request">
      <created>1756983828966</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1756983828966</updated>
    </task>
    <task id="LOCAL-00025" summary="aliyun kms secret request">
      <created>1757041381001</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1757041381001</updated>
    </task>
    <task id="LOCAL-00026" summary="aliyun kms secret request">
      <created>1757043603830</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1757043603830</updated>
    </task>
    <task id="LOCAL-00027" summary="log">
      <created>1757043692115</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1757043692115</updated>
    </task>
    <task id="LOCAL-00028" summary="auth">
      <created>1757061874673</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1757061874673</updated>
    </task>
    <task id="LOCAL-00029" summary="[fix] request fail">
      <created>1757316940144</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1757316940144</updated>
    </task>
    <task id="LOCAL-00030" summary="[fix] request fail">
      <created>1757320669985</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1757320669985</updated>
    </task>
    <option name="localTasksCounter" value="31" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="init" />
    <MESSAGE value="sha256签名" />
    <MESSAGE value="测试接口" />
    <MESSAGE value="测试yml" />
    <MESSAGE value="项目初始化" />
    <MESSAGE value="aliyun request" />
    <MESSAGE value="aliyun kms secret request" />
    <MESSAGE value="log" />
    <MESSAGE value="auth" />
    <MESSAGE value="[fix] request fail" />
    <option name="LAST_COMMIT_MESSAGE" value="[fix] request fail" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/boot/spring-boot-autoconfigure/2.1.0.RELEASE/spring-boot-autoconfigure-2.1.0.RELEASE-sources.jar!/org/springframework/boot/autoconfigure/data/redis/JedisConnectionConfiguration.java</url>
          <line>58</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-crypto/5.1.1.RELEASE/spring-security-crypto-5.1.1.RELEASE-sources.jar!/org/springframework/security/crypto/bcrypt/BCryptPasswordEncoder.java</url>
          <line>83</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/DefaultTokenServices.java</url>
          <line>300</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/DefaultTokenServices.java</url>
          <line>84</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/oauth/OAuth2AuthorizationServerConfig.java</url>
          <line>166</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/DefaultTokenServices.java</url>
          <line>121</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/DefaultTokenServices.java</url>
          <line>135</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/store/JwtTokenStore.java</url>
          <line>103</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/oauth/OAuth2AuthorizationServerConfig.java</url>
          <line>179</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/client/ClientCredentialsTokenEndpointFilter.java</url>
          <line>96</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-web/5.1.1.RELEASE/spring-security-web-5.1.1.RELEASE-sources.jar!/org/springframework/security/web/authentication/AbstractAuthenticationProcessingFilter.java</url>
          <line>198</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-web/5.1.1.RELEASE/spring-security-web-5.1.1.RELEASE-sources.jar!/org/springframework/security/web/authentication/www/BasicAuthenticationFilter.java</url>
          <line>154</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/endpoint/CheckTokenEndpoint.java</url>
          <line>72</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/oauth/RefreshTokenJwtAccessTokenConverter.java</url>
          <line>31</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-web/5.1.1.RELEASE/spring-security-web-5.1.1.RELEASE-sources.jar!/org/springframework/security/web/authentication/www/BasicAuthenticationFilter.java</url>
          <line>157</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-core/5.1.1.RELEASE/spring-security-core-5.1.1.RELEASE-sources.jar!/org/springframework/security/access/expression/SecurityExpressionRoot.java</url>
          <line>111</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-config/5.1.1.RELEASE/spring-security-config-5.1.1.RELEASE-sources.jar!/org/springframework/security/config/annotation/web/configuration/WebSecurityConfigurerAdapter.java</url>
          <line>500</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-core/5.1.1.RELEASE/spring-security-core-5.1.1.RELEASE-sources.jar!/org/springframework/security/authentication/AnonymousAuthenticationToken.java</url>
          <line>64</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/oauth/spring-security-oauth2/2.3.4.RELEASE/spring-security-oauth2-2.3.4.RELEASE-sources.jar!/org/springframework/security/oauth2/provider/token/AbstractTokenGranter.java</url>
          <line>54</line>
          <option name="timeStamp" value="54" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/AliyunKmsJwtAccessTokenConverter.java</url>
          <line>45</line>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/AliyunKmsJwtAccessTokenConverter.java</url>
          <line>79</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/security/spring-security-jwt/1.0.11.RELEASE/spring-security-jwt-1.0.11.RELEASE-sources.jar!/org/springframework/security/jwt/JwtAlgorithms.java</url>
          <line>47</line>
          <option name="timeStamp" value="59" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/oauth/OAuthClientExceptionEntryPoint.java</url>
          <line>22</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/request/KmsRequestWrapper.java</url>
          <line>42</line>
          <option name="timeStamp" value="62" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/request/KmsRequestWrapper.java</url>
          <line>39</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/aliyun/request/KmsRequestWrapper.java</url>
          <line>52</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/caidao_open_auth_service$CaidaoAuthApplication__1_.ic" NAME="CaidaoAuthApplication (1) Coverage Results" MODIFIED="1756563589519" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>