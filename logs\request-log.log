2025-09-11 14:28:52.462 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:28:52.422","method":"POST","path":"/api/open/v1/test/hello","clientIp":"*************","userAgent":"Mozilla/5.0","contentType":"application/json","headers":{"User-Agent":"Mozilla/5.0","Content-Type":"application/json"},"responseStatus":200,"responseSize":0,"duration":"6ms"}
2025-09-11 14:28:52.495 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:28:52.494","method":"POST","path":"/oauth/token","clientIp":"*************","userAgent":null,"contentType":null,"queryParams":{"password":"***","client_secret":"***"},"headers":{"Authorization":"***"},"responseStatus":200,"responseSize":0,"duration":"0ms"}
2025-09-11 14:35:36.982 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:35:36.938","method":"POST","path":"/api/open/v1/test/hello","clientIp":"*************","userAgent":"Mozilla/5.0","contentType":"application/json","headers":{"User-Agent":"Mozilla/5.0","Content-Type":"application/json"},"responseStatus":200,"responseSize":0,"duration":"9ms"}
2025-09-11 14:35:37.013 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:35:37.012","method":"POST","path":"/oauth/token","clientIp":"*************","userAgent":null,"contentType":null,"queryParams":{"password":"***","client_secret":"***"},"headers":{"Authorization":"***"},"responseStatus":200,"responseSize":0,"duration":"0ms"}
