@echo off
REM 请求日志过滤器测试脚本（Windows版本）
REM 用于测试请求日志过滤器的功能

echo === 请求日志过滤器测试 ===
echo 启动应用程序...

REM 启动应用程序（后台运行）
start /B mvn spring-boot:run -Dspring-boot.run.profiles=local

REM 等待应用程序启动
echo 等待应用程序启动...
timeout /t 30 /nobreak > nul

REM 检查应用程序是否启动成功
curl -s http://localhost:8080/api/open/v1/test/hello > nul 2>&1
if errorlevel 1 (
    echo 应用程序启动失败，退出测试
    taskkill /f /im java.exe > nul 2>&1
    exit /b 1
)

echo 应用程序启动成功，开始测试...

REM 测试1: 普通API请求
echo.
echo 测试1: 普通API请求
curl -X GET "http://localhost:8080/api/open/v1/test/hello" ^
     -H "Content-Type: application/json" ^
     -H "User-Agent: TestScript/1.0"

REM 测试2: 包含敏感数据的POST请求
echo.
echo 测试2: 包含敏感数据的POST请求
curl -X POST "http://localhost:8080/api/open/v1/kms/create" ^
     -H "Content-Type: application/json" ^
     -H "Authorization: Bearer test-token-12345" ^
     -d "{\"secretName\":\"test-secret\",\"versionId\":\"v1\",\"secretData\":\"sensitive-data\",\"password\":\"user-password\",\"client_secret\":\"client-secret-value\",\"private_key\":\"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\"}"

REM 测试3: OAuth token请求（包含敏感参数）
echo.
echo 测试3: OAuth token请求
curl -X POST "http://localhost:8080/oauth/token" ^
     -H "Content-Type: application/x-www-form-urlencoded" ^
     -H "Authorization: Basic dGVzdC1jbGllbnQ6dGVzdC1zZWNyZXQ=" ^
     -d "grant_type=client_credentials&client_id=test-client&client_secret=test-secret&scope=read"

REM 测试4: 查询参数包含敏感信息
echo.
echo 测试4: 查询参数包含敏感信息
curl -X GET "http://localhost:8080/api/open/v1/test/hello?token=sensitive-token&password=user-pwd&normal_param=value" ^
     -H "Content-Type: application/json"

echo.
echo 测试完成！
echo.
echo 请查看日志文件：
echo - 控制台输出
echo - ./logs/request-log.log
echo.

REM 等待一段时间让日志输出
timeout /t 5 /nobreak > nul

REM 显示请求日志文件的最后几行
if exist ".\logs\request-log.log" (
    echo === 请求日志文件内容（最后10行）===
    powershell "Get-Content '.\logs\request-log.log' | Select-Object -Last 10"
) else (
    echo 请求日志文件不存在，请检查应用程序配置
)

REM 停止应用程序
echo.
echo 停止应用程序...
taskkill /f /im java.exe > nul 2>&1

echo 测试结束
pause
