# 请求日志过滤器

## 概述

请求日志过滤器（RequestLoggingFilter）是一个用于记录HTTP请求详细信息的Spring Boot过滤器。它能够记录请求时间、路径、方法、请求对象、请求IP、参数等字段信息，并自动过滤敏感字段以保护数据安全。

## 功能特性

### 1. 完整的请求信息记录
- **请求时间**: 精确到毫秒的请求时间戳
- **请求路径**: 完整的请求URI
- **请求方法**: HTTP方法（GET、POST、PUT、DELETE等）
- **客户端IP**: 真实的客户端IP地址（支持代理转发）
- **用户代理**: User-Agent信息
- **内容类型**: Content-Type信息
- **查询参数**: URL查询参数
- **请求头**: HTTP请求头信息
- **请求体**: POST/PUT请求的请求体内容
- **响应状态**: HTTP响应状态码
- **响应大小**: 响应内容大小
- **处理时长**: 请求处理耗时

### 2. 敏感信息过滤
自动过滤以下敏感字段：
- **Token相关**: token, access_token, refresh_token, authorization
- **密码相关**: password, passwd, pwd
- **密钥相关**: client_secret, secret, private_key, privatekey, public_key, publickey
- **签名相关**: signature, sign
- **其他敏感**: key, keyid, secretkey, accesskey

### 3. 智能跳过机制
自动跳过以下类型的请求：
- 静态资源文件（.css, .js, .png, .jpg等）
- 健康检查接口（/actuator, /health, /metrics）

## 文件结构

```
src/main/java/com/caidaocloud/open/auth/service/infrastructure/config/filter/
├── RequestLoggingFilter.java          # 主要的过滤器实现
└── FilterConfig.java                  # 过滤器配置类

src/test/java/com/caidaocloud/open/auth/service/infrastructure/config/filter/
└── RequestLoggingFilterTest.java      # 单元测试

src/main/resources/
└── logback.xml                        # 日志配置（已更新）
```

## 配置说明

### 1. 过滤器注册
过滤器通过 `FilterConfig` 类自动注册，具有最高优先级（`Ordered.HIGHEST_PRECEDENCE + 1`）。

### 2. 日志配置
在 `logback.xml` 中配置了专门的请求日志appender：

```xml
<!-- 请求日志专用appender -->
<appender name="requestLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>./logs/request-log.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>./logs/request-log.%d{yyyy-MM-dd}-%i.log</fileNamePattern>
        <TimeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            <MaxFileSize>50MB</MaxFileSize>
        </TimeBasedFileNamingAndTriggeringPolicy>
        <maxHistory>7</maxHistory>
        <totalSizeCap>1GB</totalSizeCap>
    </rollingPolicy>
</appender>

<!-- 请求日志过滤器专用logger -->
<logger name="com.caidaocloud.open.auth.service.infrastructure.config.filter.RequestLoggingFilter" 
        level="INFO" additivity="false">
    <appender-ref ref="requestLogFile" />
    <appender-ref ref="console" />
</logger>
```

## 日志输出示例

### 1. 普通API请求
```json
{
  "requestTime": "2025-09-11 14:30:25.123",
  "method": "POST",
  "path": "/api/open/v1/test/hello",
  "clientIp": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "contentType": "application/json",
  "headers": {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  },
  "requestBody": "{\"name\":\"test\",\"value\":\"data\"}",
  "responseStatus": 200,
  "responseSize": 45,
  "duration": "125ms"
}
```

### 2. 包含敏感信息的请求（已过滤）
```json
{
  "requestTime": "2025-09-11 14:30:25.123",
  "method": "POST",
  "path": "/oauth/token",
  "clientIp": "*************",
  "userAgent": "PostmanRuntime/7.28.4",
  "contentType": "application/x-www-form-urlencoded",
  "queryParams": {
    "grant_type": "client_credentials",
    "client_secret": "***"
  },
  "headers": {
    "Authorization": "***",
    "Content-Type": "application/x-www-form-urlencoded"
  },
  "requestBody": "{\"client_id\":\"test-client\",\"client_secret\":\"***\",\"password\":\"***\"}",
  "responseStatus": 200,
  "responseSize": 256,
  "duration": "89ms"
}
```

## 使用方法

### 1. 启用过滤器
过滤器已通过 `@Component` 注解自动注册，无需额外配置即可使用。

### 2. 查看日志
请求日志会输出到以下位置：
- **控制台**: 实时查看
- **文件**: `./logs/request-log.log`

### 3. 自定义配置
如需自定义敏感字段或跳过规则，可以修改 `RequestLoggingFilter` 类中的相关常量：

```java
// 自定义敏感字段
private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
    "token", "password", "secret", "key"
    // 添加更多敏感字段...
));

// 自定义敏感字段模式
private static final List<Pattern> SENSITIVE_PATTERNS = Arrays.asList(
    Pattern.compile(".*token.*", Pattern.CASE_INSENSITIVE),
    Pattern.compile(".*password.*", Pattern.CASE_INSENSITIVE)
    // 添加更多模式...
);
```

## 性能考虑

1. **内存使用**: 过滤器会缓存请求和响应内容，对于大文件上传可能会占用较多内存
2. **处理时间**: 敏感信息过滤和JSON解析会增加少量处理时间
3. **日志文件**: 建议定期清理日志文件，避免磁盘空间不足

## 安全特性

1. **敏感信息掩码**: 所有敏感字段都会被替换为 "***"
2. **多层过滤**: 支持查询参数、请求头、JSON请求体的敏感信息过滤
3. **模式匹配**: 除了精确匹配，还支持正则表达式模式匹配敏感字段

## 测试

运行单元测试验证过滤器功能：

```bash
mvn test -Dtest=RequestLoggingFilterTest
```

测试覆盖：
- ✅ 正常请求处理
- ✅ 敏感信息过滤
- ✅ 静态资源跳过
- ✅ 客户端IP获取
- ✅ JSON敏感数据过滤
- ✅ 无效JSON处理

## 注意事项

1. **生产环境**: 建议在生产环境中适当调整日志级别，避免记录过多详细信息
2. **合规性**: 确保日志记录符合数据保护法规要求
3. **存储**: 请求日志可能包含大量数据，需要合理规划存储空间
4. **监控**: 建议配置日志监控和告警，及时发现异常请求

## 扩展功能

可以考虑添加以下扩展功能：
- 请求频率统计
- 异常请求检测
- 性能监控集成
- 日志数据分析
- 实时告警机制

## 实施完成情况

✅ **已完成的工作**：

### 1. 核心功能实现
- ✅ 创建了 `RequestLoggingFilter` 过滤器类
- ✅ 实现了完整的请求信息记录功能
- ✅ 实现了敏感信息过滤机制
- ✅ 配置了过滤器自动注册

### 2. 日志记录字段
- ✅ 请求时间（精确到毫秒）
- ✅ 请求路径和方法
- ✅ 客户端真实IP地址
- ✅ 用户代理信息
- ✅ 请求头信息
- ✅ 查询参数
- ✅ 请求体内容
- ✅ 响应状态和大小
- ✅ 请求处理耗时

### 3. 敏感信息过滤
- ✅ Token相关字段过滤
- ✅ 密码相关字段过滤
- ✅ 密钥相关字段过滤（包括KMS公钥私钥）
- ✅ 签名相关字段过滤
- ✅ 支持精确匹配和模式匹配
- ✅ 支持JSON格式敏感数据过滤

### 4. 配置和集成
- ✅ 过滤器配置类 `FilterConfig`
- ✅ 专用日志配置（独立的日志文件）
- ✅ 智能跳过静态资源和健康检查
- ✅ 高优先级过滤器顺序

### 5. 测试验证
- ✅ 完整的单元测试（11个测试用例）
- ✅ 测试覆盖所有核心功能
- ✅ 测试脚本（Windows和Linux版本）
- ✅ 所有测试通过验证

### 6. 文档和说明
- ✅ 详细的功能说明文档
- ✅ 配置和使用指南
- ✅ 日志输出示例
- ✅ 性能和安全考虑

## 部署和使用

### 立即可用
过滤器已经完全集成到项目中，无需额外配置即可使用：

1. **自动启用**: 过滤器通过 `@Component` 注解自动注册
2. **日志输出**: 请求日志会输出到控制台和 `./logs/request-log.log` 文件
3. **敏感信息保护**: 自动过滤所有敏感字段

### 验证方式
1. 启动应用程序
2. 发送HTTP请求到任何API端点
3. 查看控制台或日志文件中的请求日志
4. 验证敏感信息已被正确掩码

## 技术特点

1. **高性能**: 使用 `OncePerRequestFilter` 确保每个请求只处理一次
2. **内存安全**: 使用 `ContentCachingRequestWrapper` 安全读取请求体
3. **线程安全**: 无状态设计，支持并发请求
4. **可扩展**: 易于添加新的敏感字段或跳过规则
5. **生产就绪**: 包含完整的错误处理和性能优化

## 总结

请求日志过滤器已成功实现并集成到项目中，满足了所有要求：
- ✅ 记录完整的请求信息
- ✅ 过滤敏感字段（token、密码、client_secret、KMS密钥等）
- ✅ 支持JSON和表单数据过滤
- ✅ 独立的日志文件配置
- ✅ 完整的测试覆盖
- ✅ 生产环境可用
