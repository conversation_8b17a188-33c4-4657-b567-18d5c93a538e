package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 敏感数据过滤配置类
 * 支持通过配置文件自定义敏感字段
 * 
 * <AUTHOR>
 * @date 2025/9/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "request.logging.sensitive")
public class SensitiveConfig {

    private String mask = "***";


    private Set<String> fields = Sets.set(
            "token", "access_token", "refresh_token", "authorization",
            "password", "passwd", "pwd",
            "client_secret", "secret",
            "private_key", "privatekey", "public_key", "publickey",
            "key", "keyid", "secretkey", "accesskey",
            "signature", "sign"
    );


    /**
     * 判断字段是否为敏感字段
     * 
     * @param fieldName 字段名
     * @return 是否为敏感字段
     */
    public boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }

        String lowerFieldName = fieldName.toLowerCase();

        // 精确匹配
        if (fields.contains(lowerFieldName)) {
            return true;
        }
        return false;
    }

}
