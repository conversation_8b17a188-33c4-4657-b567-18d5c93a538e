2025-09-11 14:28:52.462 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:28:52.422","method":"POST","path":"/api/open/v1/test/hello","clientIp":"*************","userAgent":"Mozilla/5.0","contentType":"application/json","headers":{"User-Agent":"Mozilla/5.0","Content-Type":"application/json"},"responseStatus":200,"responseSize":0,"duration":"6ms"}
2025-09-11 14:28:52.495 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:28:52.494","method":"POST","path":"/oauth/token","clientIp":"*************","userAgent":null,"contentType":null,"queryParams":{"password":"***","client_secret":"***"},"headers":{"Authorization":"***"},"responseStatus":200,"responseSize":0,"duration":"0ms"}
2025-09-11 14:35:36.982 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:35:36.938","method":"POST","path":"/api/open/v1/test/hello","clientIp":"*************","userAgent":"Mozilla/5.0","contentType":"application/json","headers":{"User-Agent":"Mozilla/5.0","Content-Type":"application/json"},"responseStatus":200,"responseSize":0,"duration":"9ms"}
2025-09-11 14:35:37.013 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 14:35:37.012","method":"POST","path":"/oauth/token","clientIp":"*************","userAgent":null,"contentType":null,"queryParams":{"password":"***","client_secret":"***"},"headers":{"Authorization":"***"},"responseStatus":200,"responseSize":0,"duration":"0ms"}
2025-09-11 15:11:16.741 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 15:11:16.679","duration":"18ms","path":"/api/open/v1/test/hello","headers":{"User-Agent":"TestAgent"},"method":"GET","clientIp":"127.0.0.1","userAgent":"TestAgent","responseSize":0,"responseStatus":200,"contentType":null}
2025-09-11 15:11:16.759 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 15:11:16.748","duration":"0ms","path":"/oauth/token","method":"POST","queryParams":{"password":["secret123"],"client_secret":["client_secret_value"],"normal_param":["normal_value"]},"clientIp":"127.0.0.1","userAgent":null,"responseSize":0,"responseStatus":200,"contentType":null}
2025-09-11 15:23:34.881 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 15:23:34.802","duration":"20ms","path":"/api/open/v1/test/hello","headers":{"User-Agent":"TestAgent"},"method":"GET","clientIp":"127.0.0.1","userAgent":"TestAgent","responseSize":0,"responseStatus":200,"contentType":null}
2025-09-11 15:23:34.911 [main] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 15:23:34.896","duration":"0ms","path":"/oauth/token","method":"POST","queryParams":{"password":["secret123"],"client_secret":["client_secret_value"],"normal_param":["normal_value"]},"clientIp":"127.0.0.1","userAgent":null,"responseSize":0,"responseStatus":200,"contentType":null}
2025-09-11 16:50:05.879 [http-nio-8080-exec-1] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 16:50:05","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"headers":{"content-length":"80","postman-token":"5e5bc03a-20f8-4ea2-9457-d5fb9ac640fc","host":"127.0.0.1:8080","connection":"keep-alive","content-type":"application/x-www-form-urlencoded","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.46.0","accept":"*/*"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"113ms"}
2025-09-11 16:51:52.231 [http-nio-8080-exec-3] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 16:51:26","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"headers":{"content-length":"127","postman-token":"718395f3-9cd3-48a6-833b-9dc21d12810a","host":"127.0.0.1:8080","connection":"keep-alive","content-type":"application/x-www-form-urlencoded","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.46.0","accept":"*/*"},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":200,"responseSize":819,"duration":"25608ms"}
2025-09-11 16:51:59.997 [http-nio-8080-exec-4] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 16:51:59","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"headers":{"content-length":"80","postman-token":"581044d3-976e-4776-8484-920b14dc831f","host":"127.0.0.1:8080","connection":"keep-alive","content-type":"application/x-www-form-urlencoded","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.46.0","accept":"*/*"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"73ms"}
2025-09-11 16:54:43.234 [http-nio-8080-exec-5] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 16:54:43","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"headers":{"content-length":"80","postman-token":"d370a0bd-0041-40fd-8d69-bdd5028c4d15","host":"127.0.0.1:8080","connection":"keep-alive","content-type":"application/x-www-form-urlencoded","accept-encoding":"gzip, deflate, br","user-agent":"PostmanRuntime/7.46.0","accept":"*/*"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"146ms"}
2025-09-11 16:59:52.925 [http-nio-8080-exec-4] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 16:59:52","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"242ms"}
2025-09-11 17:02:37.053 [http-nio-8080-exec-1] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:02:36","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"377ms"}
2025-09-11 17:05:55.261 [http-nio-8080-exec-2] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:05:30","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":200,"responseSize":819,"duration":"24454ms"}
2025-09-11 17:06:03.210 [http-nio-8080-exec-7] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:06:03","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"12ms"}
2025-09-11 17:07:06.013 [http-nio-8080-exec-8] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:07:04","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"1866ms"}
2025-09-11 17:07:37.386 [http-nio-8080-exec-9] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:07:36","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"717ms"}
2025-09-11 17:09:01.736 [http-nio-8080-exec-10] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:08:01","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"59894ms"}
2025-09-11 17:09:52.529 [http-nio-8080-exec-4] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:09:51","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"1367ms"}
2025-09-11 17:11:38.335 [http-nio-8080-exec-6] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:10:09","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":500,"responseSize":68,"duration":"88533ms"}
2025-09-11 17:13:57.210 [http-nio-8080-exec-1] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:12:45","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":200,"responseSize":819,"duration":"72201ms"}
2025-09-11 17:17:27.622 [http-nio-8080-exec-2] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:14:13","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":130,"duration":"194245ms"}
2025-09-11 17:18:20.413 [http-nio-8080-exec-4] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:17:43","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"37359ms"}
2025-09-11 17:18:39.469 [http-nio-8080-exec-6] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:18:33","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"5918ms"}
2025-09-11 17:18:47.318 [http-nio-8080-exec-7] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:18:44","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"3166ms"}
2025-09-11 18:23:55.062 [http-nio-8080-exec-8] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 17:18:50","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"3904710ms"}
2025-09-11 18:30:19.627 [http-nio-8080-exec-9] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 18:26:27","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":200,"responseSize":819,"duration":"231683ms"}
2025-09-11 18:39:05.449 [http-nio-8080-exec-10] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 18:30:54","method":"POST","path":"/api/open/v1/oauth/check_token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","responseStatus":401,"responseSize":0,"duration":"491418ms"}
2025-09-11 19:09:05.500 [http-nio-8080-exec-1] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 18:56:38","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***","client_id":["68967411-b1b5-4a71-adb8-f4cc116faf5c"]},"requestBody":"client_id=68967411-b1b5-4a71-adb8-f4cc116faf5c&client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":200,"responseSize":819,"duration":"746972ms"}
2025-09-11 19:23:53.315 [http-nio-8080-exec-2] INFO  c.c.o.a.s.i.c.f.RequestLoggingFilter - Request Log: {"requestTime":"2025-09-11 19:09:49","method":"POST","path":"/api/open/v1/oauth/token","clientIp":"127.0.0.1","userAgent":"PostmanRuntime/7.46.0","contentType":"application/x-www-form-urlencoded","queryParams":{"grant_type":["client_credentials"],"client_secret":"***"},"requestBody":"client_secret=1f8e7028-2cae-4c84-a685-d280318d6240&grant_type=client_credentials","responseStatus":401,"responseSize":0,"duration":"843979ms"}
