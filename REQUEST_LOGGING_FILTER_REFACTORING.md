# 请求日志过滤器重构文档

## 重构概述

本次重构将原来的单一 `RequestLoggingFilter` 类拆分为多个专门的工具类和配置类，提高了代码的可维护性、可测试性和可扩展性。

## 重构前后对比

### 重构前
- **单一职责问题**: `RequestLoggingFilter` 类承担了太多职责
- **硬编码配置**: 敏感字段列表硬编码在代码中
- **难以测试**: 私有方法难以单独测试
- **代码重复**: 相似的功能分散在不同方法中

### 重构后
- **职责分离**: 每个类都有明确的单一职责
- **配置化**: 支持通过配置文件自定义敏感字段
- **易于测试**: 每个组件都可以独立测试
- **代码复用**: 工具类可以在其他地方复用

## 新的架构设计

### 1. 配置层 (Configuration Layer)

#### `SensitiveDataFilterConfig`
- **职责**: 管理敏感数据过滤的配置
- **特性**:
  - 支持通过 `@ConfigurationProperties` 从配置文件读取
  - 精确匹配和正则表达式模式匹配
  - 不区分大小写的字段匹配
  - 可自定义掩码字符

```yaml
# application-request-logging.yml
request:
  logging:
    sensitive:
      mask: "***"
      exact-fields:
        - password
        - token
        - client_secret
      pattern-fields:
        - ".*password.*"
        - ".*token.*"
```

### 2. 工具层 (Utility Layer)

#### `RequestInfoExtractor`
- **职责**: 从 HttpServletRequest 中提取各种信息
- **功能**:
  - 提取基本请求信息（时间、方法、路径等）
  - 获取客户端真实IP地址（支持代理）
  - 获取请求头和查询参数
  - 读取请求体内容
  - 判断是否应该跳过日志记录

#### `SensitiveDataFilter`
- **职责**: 过滤各种格式数据中的敏感信息
- **功能**:
  - 过滤 Map 类型的敏感数据
  - 过滤 JSON 格式的敏感数据
  - 过滤集合中的敏感数据
  - 递归处理嵌套数据结构
  - 支持文本中的敏感信息替换

### 3. 过滤器层 (Filter Layer)

#### `RequestLoggingFilter` (重构版)
- **职责**: 协调各个组件完成请求日志记录
- **特性**:
  - 依赖注入其他组件
  - 简洁的主要逻辑
  - 专注于过滤器的核心功能

## 重构带来的优势

### 1. 可维护性提升
- **单一职责**: 每个类都有明确的职责
- **低耦合**: 组件之间通过接口交互
- **高内聚**: 相关功能聚集在同一个类中

### 2. 可测试性提升
- **独立测试**: 每个组件都可以独立测试
- **模拟依赖**: 使用 Mockito 模拟依赖关系
- **覆盖率提高**: 更容易达到高测试覆盖率

### 3. 可扩展性提升
- **配置驱动**: 通过配置文件扩展功能
- **插件化**: 可以轻松添加新的过滤规则
- **组件复用**: 工具类可以在其他地方使用

### 4. 配置灵活性
- **外部配置**: 敏感字段可以通过配置文件修改
- **环境适配**: 不同环境可以有不同的配置
- **动态更新**: 支持配置的动态更新

## 文件结构

```
src/main/java/com/caidaocloud/open/auth/service/infrastructure/
├── config/filter/
│   ├── RequestLoggingFilter.java          # 重构后的主过滤器
│   ├── FilterConfig.java                  # 过滤器配置
│   └── SensitiveDataFilterConfig.java     # 敏感数据过滤配置
└── util/
    ├── RequestInfoExtractor.java          # 请求信息提取工具
    └── SensitiveDataFilter.java           # 敏感数据过滤工具

src/main/resources/
└── application-request-logging.yml        # 配置文件

src/test/java/com/caidaocloud/open/auth/service/infrastructure/
├── config/filter/
│   ├── RequestLoggingFilterTest.java      # 主过滤器测试
│   └── SensitiveDataFilterConfigTest.java # 配置类测试
└── util/
    ├── RequestInfoExtractorTest.java      # 提取工具测试
    └── SensitiveDataFilterTest.java       # 过滤工具测试
```

## 使用方式

### 1. 基本使用
重构后的过滤器使用方式与之前完全相同，无需修改现有代码：

```java
// 自动注入，无需手动配置
@Autowired
private RequestLoggingFilter requestLoggingFilter;
```

### 2. 自定义配置
通过配置文件自定义敏感字段：

```yaml
request:
  logging:
    sensitive:
      mask: "[FILTERED]"
      exact-fields:
        - password
        - secret_key
        - api_token
      pattern-fields:
        - ".*credential.*"
        - ".*auth.*"
```

### 3. 独立使用工具类
工具类可以在其他地方独立使用：

```java
// 提取请求信息
Map<String, Object> info = RequestInfoExtractor.extractBasicInfo(request, LocalDateTime.now());

// 过滤敏感数据
Map<String, Object> filtered = sensitiveDataFilter.filterSensitiveData(data);
```

## 测试覆盖

### 测试统计
- **RequestLoggingFilter**: 5个测试用例
- **SensitiveDataFilter**: 8个测试用例  
- **RequestInfoExtractor**: 12个测试用例
- **SensitiveDataFilterConfig**: 8个测试用例
- **总计**: 33个测试用例

### 测试类型
- **单元测试**: 测试每个组件的独立功能
- **集成测试**: 测试组件之间的协作
- **配置测试**: 测试配置的加载和应用
- **边界测试**: 测试异常情况和边界条件

## 性能影响

### 重构前后性能对比
- **内存使用**: 基本无变化
- **CPU开销**: 略有增加（依赖注入开销）
- **响应时间**: 无明显影响
- **吞吐量**: 无明显影响

### 性能优化
- **对象复用**: 减少临时对象创建
- **懒加载**: 配置对象延迟初始化
- **缓存机制**: 编译后的正则表达式缓存

## 向后兼容性

### 完全兼容
- **API接口**: 无变化
- **配置方式**: 保持原有配置有效
- **日志格式**: 输出格式完全一致
- **功能特性**: 所有原有功能保持不变

### 新增特性
- **配置文件支持**: 新增配置文件自定义功能
- **工具类复用**: 新增独立工具类
- **更好的测试**: 新增完整的测试覆盖

## 总结

本次重构成功地将单一的大类拆分为多个职责明确的小类，提高了代码的质量和可维护性。重构后的代码具有以下特点：

1. **架构清晰**: 分层明确，职责分离
2. **配置灵活**: 支持外部配置文件
3. **测试完善**: 高测试覆盖率
4. **性能稳定**: 无性能损失
5. **向后兼容**: 完全兼容现有功能

这次重构为后续的功能扩展和维护奠定了良好的基础。
