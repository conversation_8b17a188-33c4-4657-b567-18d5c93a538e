package com.caidaocloud.open.auth.service.infrastructure.config;

import com.caidaocloud.open.auth.service.infrastructure.config.filter.SensitiveConfig;
import com.caidaocloud.util.FastjsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 敏感数据过滤器
 * 用于过滤各种格式数据中的敏感信息
 * 
 * <AUTHOR>
 * @date 2025/9/11
 */
@Slf4j
@Component
public class SensitiveFilter {

    private final SensitiveConfig config;
    private final ObjectMapper objectMapper;

    @Autowired
    public SensitiveFilter(SensitiveConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 过滤Map类型的敏感数据
     * 
     * @param data 原始数据Map
     * @return 过滤后的数据Map
     */
    public Map<String, Object> filterSensitiveData(Map<String, ?> data) {
        if (data == null || data.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Object> filtered = new HashMap<>();
        
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (config.isSensitiveField(key)) {
                filtered.put(key, config.getMask());
            }  else if (value instanceof Map) {
                // 递归处理嵌套Map
                @SuppressWarnings("unchecked")
                Map<String, ?> nestedMap = (Map<String, ?>) value;
                filtered.put(key, filterSensitiveData(nestedMap));
            } else if (value instanceof Collection) {
                // 处理集合类型
                filtered.put(key, filterCollection((Collection<?>) value));
            } else {
                filtered.put(key, value);
            }
        }
        
        return filtered;
    }

    /**
     * 过滤集合中的敏感数据
     * 
     * @param collection 原始集合
     * @return 过滤后的集合
     */
    private Collection<?> filterCollection(Collection<?> collection) {
        List<Object> filtered = new ArrayList<>();
        
        for (Object item : collection) {
            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, ?> mapItem = (Map<String, ?>) item;
                filtered.add(filterSensitiveData(mapItem));
            } else if (item instanceof Collection) {
                filtered.add(filterCollection((Collection<?>) item));
            } else {
                filtered.add(item);
            }
        }
        
        return filtered;
    }

    /**
     * 过滤JSON格式的敏感数据
     * 
     * @param jsonData JSON字符串
     * @return 过滤后的JSON字符串
     */
    public String filterSensitiveJsonData(String jsonData) {
        if (jsonData == null || jsonData.trim().isEmpty()) {
            return jsonData;
        }

        try {
            Map<String, ?> map = FastjsonUtil.convertObject(jsonData, Map.class);
            Map<String, Object> map1 = filterSensitiveData(map);
            return FastjsonUtil.toJson(map1);
        } catch (Exception e) {
            log.debug("Failed to parse as JSON, treating as plain text: {}", e.getMessage());
            // 如果不是有效的JSON，直接返回原始数据
            return jsonData;
        }
    }

    /**
     * 过滤请求信息中的敏感数据
     * 
     * @param requestInfo 原始请求信息
     * @return 过滤后的请求信息
     */
    public Map<String, Object> filterRequestInfo(Map<String, Object> requestInfo) {
        if (requestInfo == null || requestInfo.isEmpty()) {
            return requestInfo;
        }

        Map<String, Object> filtered = new LinkedHashMap<>();
        
        for (Map.Entry<String, Object> entry : requestInfo.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if ("queryParams".equals(key) && value instanceof Map) {
                // 过滤查询参数
                @SuppressWarnings("unchecked")
                Map<String, ?> queryParams = (Map<String, ?>) value;
                filtered.put(key, filterSensitiveData(queryParams));
            } else if ("headers".equals(key) && value instanceof Map) {
                // 过滤请求头
                @SuppressWarnings("unchecked")
                Map<String, ?> headers = (Map<String, ?>) value;
                filtered.put(key, filterSensitiveData(headers));
            } else if ("requestBody".equals(key) && value instanceof String) {
                // 过滤请求体
                String requestBody = (String) value;
                filtered.put(key, filterSensitiveJsonData(requestBody));
            } else {
                // 其他字段直接复制
                filtered.put(key, value);
            }
        }
        
        return filtered;
    }

    /**
     * 过滤字符串中的敏感信息（简单替换）
     * 
     * @param text 原始文本
     * @param sensitiveValue 敏感值
     * @return 过滤后的文本
     */
    public String filterSensitiveText(String text, String sensitiveValue) {
        if (text == null || sensitiveValue == null) {
            return text;
        }
        
        return text.replace(sensitiveValue, config.getMask());
    }

    /**
     * 检查字段是否为敏感字段
     * 
     * @param fieldName 字段名
     * @return 是否为敏感字段
     */
    public boolean isSensitiveField(String fieldName) {
        return config.isSensitiveField(fieldName);
    }

    /**
     * 获取敏感信息掩码
     * 
     * @return 掩码字符串
     */
    public String getMask() {
        return config.getMask();
    }
}
