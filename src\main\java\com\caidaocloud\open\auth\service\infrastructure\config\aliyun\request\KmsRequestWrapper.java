package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.kms20160120.Client;
import com.aliyun.tea.TeaException;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsOperationException;
import com.caidaocloud.util.FastjsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用KMS请求包装类
 */
@Slf4j
public abstract class KmsRequestWrapper<REQ, RESP> {

    private final Client kmsClient;
    private final String methodName;

    public KmsRequestWrapper(Client kmsClient, String methodName) {
        this.kmsClient = kmsClient;
        this.methodName = methodName;
    }

    /**
     * 执行KMS操作
     */
    @SuppressWarnings("unchecked")
    public RESP execute(REQ request) {
        try {
            Method method = kmsClient.getClass().getMethod(methodName, request.getClass());
            log.info("执行KMS操作: {},request:{}", methodName, request);
            RESP response = doExecute(request, method);

            // 检查响应状态
            validateResponse(response);

            log.info("KMS操作成功: {},request:{}", methodName, request);
            return response;
        }
        catch (TeaException error) {
            log.error("KMS操作失败: {}, 错误信息: {}", methodName, error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断建议: {}", error.getData().get("Recommend"));
            }
            throw new KmsOperationException((String) error.getData().get("Message"), error);
        }
        catch (Exception error) {
            log.error("KMS操作异常: {}, 错误信息: {}", methodName, error.getMessage());
            throw new KmsOperationException("KMS操作异常: " + methodName, error);
        }
    }

    @SneakyThrows
    private RESP doExecute(REQ request, Method method)  {
        RESP response = null;
        try {
            response = (RESP) method.invoke(kmsClient, request);
        }catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
        return response;
    }

    /**
     * 验证响应状态
     *
     * @param response KMS响应对象
     * @throws KmsOperationException 当响应状态不成功时抛出异常
     */
    private void validateResponse(RESP response) throws KmsOperationException {
        if (response == null) {
            throw new KmsOperationException("KMS响应为空: " + methodName);
        }

        try {
            // 通过反射获取statusCode字段
            Method getStatusCodeMethod = response.getClass().getMethod("getStatusCode");
            Integer statusCode = (Integer) getStatusCodeMethod.invoke(response);

            // HTTP状态码200表示成功
            if (statusCode == null || statusCode != 200) {
                String errorMessage = String.format("KMS操作失败: %s, HTTP状态码: %s", methodName, statusCode);

                // 尝试获取更详细的错误信息
                try {
                    Method getBodyMethod = response.getClass().getMethod("getBody");
                    Object body = getBodyMethod.invoke(response);
                    if (body != null) {
                        // 尝试获取错误消息
                        try {
                            Method getMessageMethod = body.getClass().getMethod("getMessage");
                            String message = (String) getMessageMethod.invoke(body);
                            if (message != null && !message.isEmpty()) {
                                errorMessage += ", 错误消息: " + message;
                            }
                        } catch (Exception e) {
                            // 忽略获取消息失败的异常
                        }

                        // 尝试获取错误代码
                        try {
                            Method getCodeMethod = body.getClass().getMethod("getCode");
                            String code = (String) getCodeMethod.invoke(body);
                            if (code != null && !code.isEmpty()) {
                                errorMessage += ", 错误代码: " + code;
                            }
                        } catch (Exception e) {
                            // 忽略获取错误代码失败的异常
                        }
                    }
                } catch (Exception e) {
                    // 忽略获取响应体失败的异常
                }

                log.error(errorMessage);
                throw new KmsOperationException(errorMessage);
            }

        } catch (KmsOperationException e) {
            // 重新抛出KmsOperationException
            throw e;
        } catch (Exception e) {
            // 如果无法获取状态码，记录警告但不抛出异常
            log.warn("无法验证KMS响应状态: {}, 错误: {}", methodName, e.getMessage());
        }
    }

    public RequestBuilder<REQ> builder(Class<REQ> reqClass) {
        return new RequestBuilder<>(reqClass);
    }

    public RequestBuilder<REQ> builder() {
        return new RequestBuilder<>(genericClass());
    }

    public Class<REQ> genericClass() {
        // 获取当前类的泛型超类
        Type superClass = getClass().getGenericSuperclass();

        // 如果是参数化类型
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            // 获取泛型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                // 返回第一个泛型参数的 Class
                return (Class<REQ>) actualTypeArguments[0];
            }
        }

        // 如果无法获取泛型类型，返回 null 或抛出异常
        throw new RuntimeException("Unable to determine generic type");
    }

    // 通用请求Builder
    public static class RequestBuilder<REQ> {
        private final Class<REQ> request;
        private final Map<String, Object> parameters;

        public RequestBuilder(Class<REQ> requestClass) {
            try {
                this.request = requestClass;
                this.parameters = new HashMap<>();
            } catch (Exception e) {
                throw new ServerException("Failed to instantiate request: " + e.getMessage(), e);
            }
        }

        public RequestBuilder<REQ> setParameter(String key, Object value) {
            parameters.put(key, value);
            return this;
        }

        public REQ build() {
            return FastjsonUtil.convertObject(parameters, request);
        }
    }
}