package com.caidaocloud.open.auth.service.infrastructure.util;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * RequestInfoExtractor 测试类
 * 
 * <AUTHOR>
 * @date 2025/9/11
 */
public class RequestInfoExtractorTest {

    private MockHttpServletRequest request;

    @Before
    public void setUp() {
        request = new MockHttpServletRequest();
    }

    @Test
    public void testExtractBasicInfo() {
        // 设置请求信息
        request.setMethod("POST");
        request.setRequestURI("/api/test");
        request.setRemoteAddr("*************");
        request.addHeader("User-Agent", "TestAgent");
        request.setContentType("application/json");


        Map<String, Object> result = RequestUtil.extractBasicInfo(request, System.currentTimeMillis());

        assertNotNull(result.get("requestTime"));
        assertEquals("POST", result.get("method"));
        assertEquals("/api/test", result.get("path"));
        assertEquals("*************", result.get("clientIp"));
        assertEquals("TestAgent", result.get("userAgent"));
        assertEquals("application/json", result.get("contentType"));
    }

    @Test
    public void testGetClientIpAddress_XForwardedFor() {
        // 测试X-Forwarded-For头
        request.addHeader("X-Forwarded-For", "*************, **********, ***************");

        String result = RequestUtil.getClientIpAddress(request);

        assertEquals("*************", result);
    }

    @Test
    public void testGetClientIpAddress_XRealIP() {
        // 测试X-Real-IP头
        request.addHeader("X-Real-IP", "*************");

        String result = RequestUtil.getClientIpAddress(request);

        assertEquals("*************", result);
    }

    @Test
    public void testGetClientIpAddress_RemoteAddr() {
        // 测试RemoteAddr
        request.setRemoteAddr("*************");

        String result = RequestUtil.getClientIpAddress(request);

        assertEquals("*************", result);
    }

    @Test
    public void testGetRequestHeaders() {
        // 测试获取请求头
        request.addHeader("Content-Type", "application/json");
        request.addHeader("User-Agent", "TestAgent");
        request.addHeader("Authorization", "Bearer token123");

        Map<String, String> result = RequestUtil.getRequestHeaders(request);

        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("TestAgent", result.get("User-Agent"));
        assertEquals("Bearer token123", result.get("Authorization"));
    }

    @Test
    public void testGetQueryParameters() {
        // 测试获取查询参数
        request.setParameter("username", "testuser");
        request.setParameter("password", "secret123");
        request.setParameter("page", "1");

        Map<String, String[]> result = RequestUtil.getQueryParameters(request);

        assertEquals("testuser", result.get("username")[0]);
        assertEquals("secret123", result.get("password")[0]);
        assertEquals("1", result.get("page")[0]);
    }

    @Test
    public void testGetRequestBody_WithWrapper() {
        // 测试获取请求体（使用ContentCachingRequestWrapper）
        ContentCachingRequestWrapper wrapper = new ContentCachingRequestWrapper(request);

        String result = RequestUtil.getRequestBody(wrapper);

        // 由于MockHttpServletRequest的限制，ContentCachingRequestWrapper可能返回空内容
        // 这里主要测试方法不会抛出异常，结果可能为null或空字符串
        // 在实际使用中，需要先读取请求内容才能获取到数据
        assertTrue("Result should be null or empty", result == null || result.isEmpty());
    }

    @Test
    public void testGetRequestBody_WithoutWrapper() {
        // 测试获取请求体（不使用ContentCachingRequestWrapper）
        String result = RequestUtil.getRequestBody(request);

        // 应该返回null并记录警告
        assertNull(result);
    }

    @Test
    public void testShouldSkipLogging_StaticResources() {
        // 测试静态资源跳过
        request.setRequestURI("/static/css/style.css");
        assertTrue(RequestUtil.shouldSkipLogging(request));

        request.setRequestURI("/js/app.js");
        assertTrue(RequestUtil.shouldSkipLogging(request));

        request.setRequestURI("/images/logo.png");
        assertTrue(RequestUtil.shouldSkipLogging(request));
    }

    @Test
    public void testShouldSkipLogging_HealthCheck() {
        // 测试健康检查接口跳过
        request.setRequestURI("/actuator/health");
        assertTrue(RequestUtil.shouldSkipLogging(request));

        request.setRequestURI("/health");
        assertTrue(RequestUtil.shouldSkipLogging(request));

        request.setRequestURI("/metrics");
        assertTrue(RequestUtil.shouldSkipLogging(request));
    }

    @Test
    public void testShouldSkipLogging_ApiRequest() {
        // 测试API请求不跳过
        request.setRequestURI("/api/open/v1/test");
        assertFalse(RequestUtil.shouldSkipLogging(request));

        request.setRequestURI("/oauth/token");
        assertFalse(RequestUtil.shouldSkipLogging(request));
    }

    @Test
    public void testBuildRequestInfo() {
        // 测试构建完整请求信息
        request.setMethod("POST");
        request.setRequestURI("/api/test");
        request.setRemoteAddr("*************");
        request.addHeader("User-Agent", "TestAgent");
        request.setParameter("username", "testuser");

        long startTime = System.currentTimeMillis();

        Map<String, Object> result = RequestUtil.buildRequestInfo(
                request, startTime, 200, 1024L);

        assertEquals("POST", result.get("method"));
        assertEquals("/api/test", result.get("path"));
        assertEquals("*************", result.get("clientIp"));
        assertEquals("TestAgent", result.get("userAgent"));
        assertEquals(200, result.get("responseStatus"));
        assertEquals(1024L, result.get("responseSize"));
        assertNotNull(result.get("duration"));
        assertNotNull(result.get("queryParams"));
        assertNotNull(result.get("headers"));
    }
}
