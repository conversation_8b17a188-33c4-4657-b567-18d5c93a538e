package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 请求日志过滤器测试类
 *
 * <AUTHOR>
 * @date 2025/9/11
 */
public class RequestLoggingFilterTest {

    private RequestLoggingFilter requestLoggingFilter;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private MockFilterChain filterChain;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        requestLoggingFilter = new RequestLoggingFilter();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testDoFilterInternal_NormalRequest() throws ServletException, IOException {
        // 设置正常请求
        request.setMethod("POST");
        request.setRequestURI("/api/open/v1/test/hello");
        request.setRemoteAddr("*************");
        request.addHeader("User-Agent", "Mozilla/5.0");
        request.addHeader("Content-Type", "application/json");
        request.setContent("{\"name\":\"test\",\"value\":\"data\"}".getBytes());

        // 执行过滤器
        try {
            requestLoggingFilter.doFilterInternal(request, response, filterChain);
            // 验证请求被正常处理
            assertNotNull(filterChain.getRequest());
        } catch (Exception e) {
            fail("Filter should not throw exception: " + e.getMessage());
        }
    }

    @Test
    public void testDoFilterInternal_WithSensitiveData() throws ServletException, IOException {
        // 设置包含敏感数据的请求
        request.setMethod("POST");
        request.setRequestURI("/oauth/token");
        request.setRemoteAddr("*************");
        request.addHeader("Authorization", "Bearer sensitive-token");
        request.addParameter("client_secret", "secret-value");
        request.addParameter("password", "user-password");
        request.setContent("{\"token\":\"sensitive-token\",\"private_key\":\"private-key-data\"}".getBytes());

        // 执行过滤器
        try {
            requestLoggingFilter.doFilterInternal(request, response, filterChain);
            // 验证请求被正常处理
            assertNotNull(filterChain.getRequest());
        } catch (Exception e) {
            fail("Filter should not throw exception: " + e.getMessage());
        }
    }

    @Test
    public void testShouldSkipLogging_StaticResources() throws Exception {
        // 测试静态资源跳过
        request.setRequestURI("/static/css/style.css");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("shouldSkipLogging",
                javax.servlet.http.HttpServletRequest.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(requestLoggingFilter, request);
        assertTrue(result);
    }

    @Test
    public void testShouldSkipLogging_HealthCheck() throws Exception {
        // 测试健康检查接口跳过
        request.setRequestURI("/actuator/health");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("shouldSkipLogging",
                javax.servlet.http.HttpServletRequest.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(requestLoggingFilter, request);
        assertTrue(result);
    }

    @Test
    public void testShouldSkipLogging_ApiRequest() throws Exception {
        // 测试API请求不跳过
        request.setRequestURI("/api/open/v1/test");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("shouldSkipLogging",
                javax.servlet.http.HttpServletRequest.class);
        method.setAccessible(true);

        boolean result = (boolean) method.invoke(requestLoggingFilter, request);
        assertFalse(result);
    }

    @Test
    public void testGetClientIpAddress_XForwardedFor() throws Exception {
        // 测试X-Forwarded-For头
        request.addHeader("X-Forwarded-For", "*************, **********, ***************");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("getClientIpAddress",
                javax.servlet.http.HttpServletRequest.class);
        method.setAccessible(true);

        String result = (String) method.invoke(requestLoggingFilter, request);
        assertEquals("*************", result);
    }

    @Test
    public void testGetClientIpAddress_RemoteAddr() throws Exception {
        // 测试RemoteAddr
        request.setRemoteAddr("*************");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("getClientIpAddress",
                javax.servlet.http.HttpServletRequest.class);
        method.setAccessible(true);

        String result = (String) method.invoke(requestLoggingFilter, request);
        assertEquals("*************", result);
    }

    @Test
    public void testFilterSensitiveData_Map() throws Exception {
        // 测试Map类型敏感数据过滤
        Map<String, Object> data = new HashMap<>();
        data.put("username", "testuser");
        data.put("password", "secret123");
        data.put("token", "bearer-token");
        data.put("client_secret", "client-secret");
        data.put("normal_field", "normal_value");

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("filterSensitiveData",
                java.util.Map.class);
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(requestLoggingFilter, data);

        assertEquals("testuser", result.get("username"));
        assertEquals("***", result.get("password"));
        assertEquals("***", result.get("token"));
        assertEquals("***", result.get("client_secret"));
        assertEquals("normal_value", result.get("normal_field"));
    }

    @Test
    public void testFilterSensitiveJsonData() throws Exception {
        // 测试JSON敏感数据过滤
        String jsonData = "{\"username\":\"testuser\",\"password\":\"secret123\",\"token\":\"bearer-token\",\"data\":{\"private_key\":\"key-data\"}}";

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("filterSensitiveJsonData",
                String.class);
        method.setAccessible(true);

        String result = (String) method.invoke(requestLoggingFilter, jsonData);

        // 验证敏感字段被掩码
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"token\":\"***\""));
        assertTrue(result.contains("\"private_key\":\"***\""));
        // 验证非敏感字段保持不变
        assertTrue(result.contains("\"username\":\"testuser\""));
    }

    @Test
    public void testIsSensitiveField() throws Exception {
        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("isSensitiveField",
                String.class);
        method.setAccessible(true);

        // 测试精确匹配
        assertTrue((boolean) method.invoke(requestLoggingFilter, "password"));
        assertTrue((boolean) method.invoke(requestLoggingFilter, "token"));
        assertTrue((boolean) method.invoke(requestLoggingFilter, "client_secret"));

        // 测试模式匹配
        assertTrue((boolean) method.invoke(requestLoggingFilter, "access_token"));
        assertTrue((boolean) method.invoke(requestLoggingFilter, "private_key"));
        assertTrue((boolean) method.invoke(requestLoggingFilter, "user_password"));

        // 测试非敏感字段
        assertFalse((boolean) method.invoke(requestLoggingFilter, "username"));
        assertFalse((boolean) method.invoke(requestLoggingFilter, "email"));
        assertFalse((boolean) method.invoke(requestLoggingFilter, "data"));

        // 测试null值
        assertFalse((boolean) method.invoke(requestLoggingFilter, (String) null));
    }

    @Test
    public void testFilterSensitiveJsonData_InvalidJson() throws Exception {
        // 测试无效JSON
        String invalidJson = "invalid json data";

        java.lang.reflect.Method method = RequestLoggingFilter.class.getDeclaredMethod("filterSensitiveJsonData",
                String.class);
        method.setAccessible(true);

        String result = (String) method.invoke(requestLoggingFilter, invalidJson);

        // 无效JSON应该原样返回
        assertEquals(invalidJson, result);
    }
}
