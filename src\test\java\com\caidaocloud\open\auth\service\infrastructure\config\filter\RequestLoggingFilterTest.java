package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import com.caidaocloud.open.auth.service.infrastructure.config.SensitiveFilter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 请求日志过滤器测试类（重构版本）
 *
 * <AUTHOR>
 * @date 2025/9/11
 */
public class RequestLoggingFilterTest {

    private RequestLoggingFilter requestLoggingFilter;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private MockFilterChain filterChain;

    @Mock
    private SensitiveFilter sensitiveDataFilter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 模拟SensitiveDataFilter的行为
        when(sensitiveDataFilter.filterRequestInfo(any())).thenAnswer(invocation -> {
            Map<String, Object> input = invocation.getArgument(0);
            Map<String, Object> filtered = new HashMap<>(input);
            return filtered;
        });

        requestLoggingFilter = new RequestLoggingFilter(sensitiveDataFilter);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();
    }

    @Test
    public void testDoFilterInternal_NormalRequest() throws ServletException, IOException {
        // 设置正常请求
        request.setMethod("GET");
        request.setRequestURI("/api/open/v1/test/hello");
        request.addHeader("User-Agent", "TestAgent");

        // 执行过滤器
        requestLoggingFilter.doFilterInternal(request, response, filterChain);

        // 验证请求被正常处理
        assertTrue("Filter chain should be called", filterChain.getRequest() != null);
    }

    @Test
    public void testDoFilterInternal_WithSensitiveData() throws ServletException, IOException {
        // 设置包含敏感数据的请求
        request.setMethod("POST");
        request.setRequestURI("/oauth/token");
        request.setParameter("password", "secret123");
        request.setParameter("client_secret", "client_secret_value");
        request.setParameter("normal_param", "normal_value");

        // 执行过滤器
        requestLoggingFilter.doFilterInternal(request, response, filterChain);

        // 验证请求被正常处理
        assertTrue("Filter chain should be called", filterChain.getRequest() != null);
    }

    @Test
    public void testDoFilterInternal_StaticResource() throws ServletException, IOException {
        // 设置静态资源请求
        request.setMethod("GET");
        request.setRequestURI("/static/css/style.css");

        // 执行过滤器
        requestLoggingFilter.doFilterInternal(request, response, filterChain);

        // 验证请求被正常处理
        assertTrue("Filter chain should be called", filterChain.getRequest() != null);
    }

    @Test
    public void testDoFilterInternal_HealthCheck() throws ServletException, IOException {
        // 设置健康检查请求
        request.setMethod("GET");
        request.setRequestURI("/actuator/health");

        // 执行过滤器
        requestLoggingFilter.doFilterInternal(request, response, filterChain);

        // 验证请求被正常处理
        assertTrue("Filter chain should be called", filterChain.getRequest() != null);
    }

}
