# 请求日志过滤器配置
request:
  logging:
    sensitive:
      # 敏感信息掩码
      mask: "***"
      
      # 精确匹配的敏感字段列表（不区分大小写）
      exact-fields:
        - token
        - access_token
        - refresh_token
        - authorization
        - password
        - passwd
        - pwd
        - client_secret
        - secret
        - private_key
        - privatekey
        - public_key
        - publickey
        - key
        - keyid
        - secretkey
        - accesskey
        - signature
        - sign
        - api_key
        - apikey
        - session_id
        - sessionid
        - csrf_token
        - csrftoken
        
      # 模式匹配的敏感字段列表（正则表达式，不区分大小写）
      pattern-fields:
        - ".*token.*"
        - ".*password.*"
        - ".*secret.*"
        - ".*key.*"
        - ".*sign.*"
        - ".*auth.*"
        - ".*credential.*"
        - ".*session.*"
