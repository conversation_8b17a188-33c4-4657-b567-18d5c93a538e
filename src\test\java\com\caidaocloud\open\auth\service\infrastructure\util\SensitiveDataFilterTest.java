package com.caidaocloud.open.auth.service.infrastructure.util;

import com.caidaocloud.open.auth.service.infrastructure.config.SensitiveFilter;
import com.caidaocloud.open.auth.service.infrastructure.config.filter.SensitiveConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * SensitiveDataFilter 测试类
 * 
 * <AUTHOR>
 * @date 2025/9/11
 */
public class SensitiveDataFilterTest {

    private SensitiveFilter sensitiveDataFilter;
    
    @Mock
    private SensitiveConfig config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 模拟配置行为
        when(config.getMask()).thenReturn("***");
        when(config.isSensitiveField("password")).thenReturn(true);
        when(config.isSensitiveField("token")).thenReturn(true);
        when(config.isSensitiveField("client_secret")).thenReturn(true);
        when(config.isSensitiveField("username")).thenReturn(false);
        when(config.isSensitiveField("email")).thenReturn(false);
        
        sensitiveDataFilter = new SensitiveFilter(config);
    }

    @Test
    public void testFilterSensitiveData_Map() {
        // 测试Map类型敏感数据过滤
        Map<String, Object> data = new HashMap<>();
        data.put("username", "testuser");
        data.put("password", "secret123");
        data.put("token", "bearer-token");
        data.put("client_secret", "client-secret");
        data.put("normal_field", "normal_value");

        Map<String, Object> result = sensitiveDataFilter.filterSensitiveData(data);

        assertEquals("testuser", result.get("username"));
        assertEquals("***", result.get("password"));
        assertEquals("***", result.get("token"));
        assertEquals("***", result.get("client_secret"));
        assertEquals("normal_value", result.get("normal_field"));
    }

    @Test
    public void testFilterSensitiveJsonData() {
        // 测试JSON敏感数据过滤
        String jsonData = "{\"username\":\"testuser\",\"password\":\"secret123\",\"token\":\"bearer-token\"}";

        String result = sensitiveDataFilter.filterSensitiveJsonData(jsonData);

        // 验证敏感字段被掩码
        assertTrue(result.contains("\"password\":\"***\""));
        assertTrue(result.contains("\"token\":\"***\""));
        // 验证非敏感字段保持不变
        assertTrue(result.contains("\"username\":\"testuser\""));
    }

    @Test
    public void testFilterSensitiveJsonData_InvalidJson() {
        // 测试无效JSON
        String invalidJson = "invalid json data";

        String result = sensitiveDataFilter.filterSensitiveJsonData(invalidJson);

        // 无效JSON应该原样返回
        assertEquals(invalidJson, result);
    }

    @Test
    public void testFilterRequestInfo() {
        // 测试请求信息过滤
        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("method", "POST");
        requestInfo.put("path", "/api/test");
        
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("password", "secret123");
        queryParams.put("username", "testuser");
        requestInfo.put("queryParams", queryParams);
        
        Map<String, Object> headers = new HashMap<>();
        headers.put("Authorization", "Bearer token123");
        headers.put("Content-Type", "application/json");
        requestInfo.put("headers", headers);
        
        requestInfo.put("requestBody", "{\"token\":\"secret-token\"}");

        Map<String, Object> result = sensitiveDataFilter.filterRequestInfo(requestInfo);

        assertEquals("POST", result.get("method"));
        assertEquals("/api/test", result.get("path"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> filteredQueryParams = (Map<String, Object>) result.get("queryParams");
        assertEquals("***", filteredQueryParams.get("password"));
        assertEquals("testuser", filteredQueryParams.get("username"));
        
        assertTrue(result.get("requestBody").toString().contains("\"token\":\"***\""));
    }

    @Test
    public void testFilterSensitiveText() {
        // 测试文本敏感信息过滤
        String text = "User password is secret123 and token is abc123";
        String result = sensitiveDataFilter.filterSensitiveText(text, "secret123");
        
        assertEquals("User password is *** and token is abc123", result);
    }

    @Test
    public void testIsSensitiveField() {
        // 测试敏感字段判断
        assertTrue(sensitiveDataFilter.isSensitiveField("password"));
        assertTrue(sensitiveDataFilter.isSensitiveField("token"));
        assertFalse(sensitiveDataFilter.isSensitiveField("username"));
    }

    @Test
    public void testGetMask() {
        // 测试获取掩码
        assertEquals("***", sensitiveDataFilter.getMask());
    }
}
