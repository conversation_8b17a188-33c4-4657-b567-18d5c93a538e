package com.caidaocloud.open.auth.service.infrastructure.util;

import com.caidaocloud.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 请求信息提取工具类
 *
 * <AUTHOR>
 * @date 2025/9/11
 */
@Slf4j
public class RequestUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 提取请求的基本信息
     * 
     * @param request HTTP请求对象
     * @param requestTime 请求时间
     * @return 请求基本信息Map
     */
    public static Map<String, Object> extractBasicInfo(HttpServletRequest request, long requestTime) {
        Map<String, Object> basicInfo = new LinkedHashMap<>();
        
        basicInfo.put("requestTime", DateUtil.formatTime(requestTime));
        basicInfo.put("method", request.getMethod());
        basicInfo.put("path", request.getRequestURI());
        basicInfo.put("clientIp", getClientIpAddress(request));
        basicInfo.put("userAgent", request.getHeader("User-Agent"));
        basicInfo.put("contentType", request.getContentType());
        
        return basicInfo;
    }

    /**
     * 获取客户端真实IP地址
     * 支持通过代理服务器获取真实IP
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取请求头信息
     *
     * @param request HTTP请求对象
     * @return 请求头Map
     */
    public static Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }

        return headers;
    }

    /**
     * 获取查询参数
     * 
     * @param request HTTP请求对象
     * @return 查询参数Map
     */
    public static Map<String, String[]> getQueryParameters(HttpServletRequest request) {
        return request.getParameterMap();
    }

    /**
     * 获取请求体内容
     * 
     * @param request HTTP请求对象（必须是ContentCachingRequestWrapper）
     * @return 请求体字符串，如果无法读取则返回null
     */
    public static String getRequestBody(HttpServletRequest request) {
        if (!(request instanceof ContentCachingRequestWrapper)) {
            log.warn("Request is not wrapped with ContentCachingRequestWrapper, cannot read body");
            return null;
        }
        
        try {
            ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
            byte[] content = wrapper.getContentAsByteArray();
            if (content.length > 0) {
                String encoding = wrapper.getCharacterEncoding();
                if (encoding == null) {
                    encoding = "UTF-8";
                }
                return new String(content, encoding);
            }
        } catch (Exception e) {
            log.warn("Failed to read request body: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * 判断是否应该跳过日志记录
     * 
     * @param request HTTP请求对象
     * @return 是否跳过
     */
    public static boolean shouldSkipLogging(HttpServletRequest request) {
        String uri = request.getRequestURI();
        
        // 跳过静态资源
        if (isStaticResource(uri)) {
            return true;
        }
        
        // 跳过健康检查和监控接口
        if (isHealthCheckEndpoint(uri)) {
            return true;
        }
        
        return false;
    }

    /**
     * 判断是否为静态资源
     * 
     * @param uri 请求URI
     * @return 是否为静态资源
     */
    private static boolean isStaticResource(String uri) {
        if (!uri.contains(".")) {
            return false;
        }
        
        String[] staticExtensions = {
            ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", 
            ".svg", ".woff", ".woff2", ".ttf", ".eot", ".map"
        };
        
        String lowerUri = uri.toLowerCase();
        for (String extension : staticExtensions) {
            if (lowerUri.endsWith(extension)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断是否为健康检查端点
     * 
     * @param uri 请求URI
     * @return 是否为健康检查端点
     */
    private static boolean isHealthCheckEndpoint(String uri) {
        String[] healthCheckPaths = {
            "/actuator", "/health", "/metrics", "/info", "/status"
        };
        
        for (String path : healthCheckPaths) {
            if (uri.contains(path)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 构建完整的请求信息
     * 
     * @param request HTTP请求对象
     * @param requestTime 请求时间
     * @param responseStatus 响应状态码
     * @param responseSize 响应大小
     * @return 完整的请求信息Map
     */
    public static Map<String, Object> buildRequestInfo(HttpServletRequest request, 
                                                      long requestTime,
                                                      int responseStatus,
                                                      long responseSize) {
        Map<String, Object> requestInfo = extractBasicInfo(request, requestTime);
        
        // 添加查询参数
        Map<String, String[]> queryParams = getQueryParameters(request);
        if (!queryParams.isEmpty()) {
            requestInfo.put("queryParams", queryParams);
        }
        
        // // 添加请求头
        // Map<String, String> headers = getRequestHeaders(request);
        // if (!headers.isEmpty()) {
        //     requestInfo.put("headers", headers);
        // }
        
        // 添加请求体
        String requestBody = getRequestBody(request);
        if (StringUtils.hasText(requestBody)) {
            requestInfo.put("requestBody", requestBody);
        }
        
        // 添加响应信息
        requestInfo.put("responseStatus", responseStatus);
        requestInfo.put("responseSize", responseSize);
        requestInfo.put("duration", (System.currentTimeMillis() - requestTime) + "ms");
        
        return requestInfo;
    }
}
