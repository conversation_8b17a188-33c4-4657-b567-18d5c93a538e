package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import com.caidaocloud.open.auth.service.infrastructure.util.RequestUtil;
import com.caidaocloud.open.auth.service.infrastructure.config.SensitiveFilter;
import com.caidaocloud.util.FastjsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 请求日志过滤器
 * 记录请求时间、路径、方法、请求对象、请求IP、参数等信息
 * 过滤敏感字段：token、密码、client_secret、kms中的公钥私钥
 *
 * <AUTHOR> Zhou
 * @date 2025/9/11
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class RequestLoggingFilter extends OncePerRequestFilter {

    private final SensitiveFilter sensitiveDataFilter;

    @Autowired
    public RequestLoggingFilter(SensitiveFilter sensitiveDataFilter) {
        this.sensitiveDataFilter = sensitiveDataFilter;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        // 跳过静态资源和健康检查接口
        if (RequestUtil.shouldSkipLogging(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 包装请求和响应以便读取内容
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);

        long requestTime = System.currentTimeMillis();

        try {
            // 执行请求
            filterChain.doFilter(wrappedRequest, wrappedResponse);
        } finally {
            // 记录请求日志
            logRequest(wrappedRequest, wrappedResponse, requestTime);

            // 复制响应内容到原始响应
            wrappedResponse.copyBodyToResponse();
        }
    }

    /**
     * 记录请求日志
     */
    private void logRequest(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response,
             long requestTime) {
        try {
            // 使用RequestInfoExtractor构建请求信息
            Map<String, Object> requestInfo = RequestUtil.buildRequestInfo(
                    request, requestTime, response.getStatus(), response.getContentSize());

            // 使用SensitiveDataFilter过滤敏感信息
            Map<String, Object> filteredRequestInfo = sensitiveDataFilter.filterRequestInfo(requestInfo);

            // 输出日志
            log.info("Request Log: {}", FastjsonUtil.toJson(filteredRequestInfo));

        } catch (Exception e) {
            log.warn("Failed to log request: {}", e.getMessage());
        }
    }
}