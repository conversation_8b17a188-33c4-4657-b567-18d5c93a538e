package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 请求日志过滤器
 * 记录请求时间、路径、方法、请求对象、请求IP、参数等信息
 * 过滤敏感字段：token、密码、client_secret、kms中的公钥私钥
 * 
 * <AUTHOR> Zhou
 * @date 2025/9/11
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class RequestLoggingFilter extends OncePerRequestFilter {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 敏感字段列表（不区分大小写）
     */
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
        "token", "access_token", "refresh_token", "authorization",
        "password", "passwd", "pwd",
        "client_secret", "secret",
        "private_key", "privatekey", "public_key", "publickey",
        "key", "keyid", "secretkey", "accesskey",
        "signature", "sign"
    ));
    
    /**
     * 敏感字段的正则表达式模式
     */
    private static final List<Pattern> SENSITIVE_PATTERNS = Arrays.asList(
        Pattern.compile(".*token.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*password.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*secret.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*key.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*sign.*", Pattern.CASE_INSENSITIVE)
    );
    
    /**
     * 敏感信息掩码
     */
    private static final String MASK = "***";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 跳过静态资源和健康检查接口
        if (shouldSkipLogging(request)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 包装请求和响应以便读取内容
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);
        
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        LocalDateTime requestTime = LocalDateTime.now();
        
        try {
            // 执行请求
            filterChain.doFilter(wrappedRequest, wrappedResponse);
        } finally {
            // 记录请求日志
            logRequest(wrappedRequest, wrappedResponse, requestTime, startTime);
            
            // 复制响应内容到原始响应
            wrappedResponse.copyBodyToResponse();
        }
    }

    /**
     * 判断是否跳过日志记录
     */
    private boolean shouldSkipLogging(HttpServletRequest request) {
        String uri = request.getRequestURI();
        
        // 跳过静态资源
        if (uri.contains(".") && (uri.endsWith(".css") || uri.endsWith(".js") || 
            uri.endsWith(".png") || uri.endsWith(".jpg") || uri.endsWith(".ico") ||
            uri.endsWith(".gif") || uri.endsWith(".svg") || uri.endsWith(".woff") ||
            uri.endsWith(".woff2") || uri.endsWith(".ttf"))) {
            return true;
        }
        
        // 跳过健康检查和监控接口
        if (uri.contains("/actuator") || uri.contains("/health") || uri.contains("/metrics")) {
            return true;
        }
        
        return false;
    }

    /**
     * 记录请求日志
     */
    private void logRequest(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response,
                           LocalDateTime requestTime, long startTime) {
        try {
            long duration = System.currentTimeMillis() - startTime;
            
            // 构建日志信息
            Map<String, Object> logInfo = new LinkedHashMap<>();
            logInfo.put("requestTime", requestTime.format(DATE_TIME_FORMATTER));
            logInfo.put("method", request.getMethod());
            logInfo.put("path", request.getRequestURI());
            logInfo.put("clientIp", getClientIpAddress(request));
            logInfo.put("userAgent", request.getHeader("User-Agent"));
            logInfo.put("contentType", request.getContentType());
            
            // 添加查询参数（过滤敏感信息）
            Map<String, String[]> queryParams = request.getParameterMap();
            if (!queryParams.isEmpty()) {
                logInfo.put("queryParams", filterSensitiveData(queryParams));
            }
            
            // 添加请求头（过滤敏感信息）
            Map<String, String> headers = getRequestHeaders(request);
            if (!headers.isEmpty()) {
                logInfo.put("headers", filterSensitiveData(headers));
            }
            
            // 添加请求体（过滤敏感信息）
            String requestBody = getRequestBody(request);
            if (StringUtils.hasText(requestBody)) {
                logInfo.put("requestBody", filterSensitiveJsonData(requestBody));
            }
            
            // 添加响应信息
            logInfo.put("responseStatus", response.getStatus());
            logInfo.put("responseSize", response.getContentSize());
            logInfo.put("duration", duration + "ms");
            
            // 输出日志
            log.info("Request Log: {}", objectMapper.writeValueAsString(logInfo));
            
        } catch (Exception e) {
            log.warn("Failed to log request: {}", e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取请求头
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        
        return headers;
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(ContentCachingRequestWrapper request) {
        try {
            byte[] content = request.getContentAsByteArray();
            if (content.length > 0) {
                return new String(content, request.getCharacterEncoding());
            }
        } catch (Exception e) {
            log.warn("Failed to read request body: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 过滤Map类型的敏感数据
     */
    private Map<String, Object> filterSensitiveData(Map<String, ?> data) {
        Map<String, Object> filtered = new HashMap<>();
        
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (isSensitiveField(key)) {
                filtered.put(key, MASK);
            } else if (value instanceof String[]) {
                // 处理数组类型的值
                String[] arrayValue = (String[]) value;
                if (arrayValue.length == 1) {
                    filtered.put(key, arrayValue[0]);
                } else {
                    filtered.put(key, Arrays.asList(arrayValue));
                }
            } else {
                filtered.put(key, value);
            }
        }
        
        return filtered;
    }

    /**
     * 过滤JSON格式的敏感数据
     */
    private String filterSensitiveJsonData(String jsonData) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonData);
            JsonNode filteredNode = filterJsonNode(rootNode);
            return objectMapper.writeValueAsString(filteredNode);
        } catch (Exception e) {
            // 如果不是有效的JSON，直接返回原始数据
            return jsonData;
        }
    }

    /**
     * 递归过滤JSON节点中的敏感数据
     */
    private JsonNode filterJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = objectMapper.createObjectNode();
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldName = field.getKey();
                JsonNode fieldValue = field.getValue();
                
                if (isSensitiveField(fieldName)) {
                    objectNode.put(fieldName, MASK);
                } else {
                    objectNode.set(fieldName, filterJsonNode(fieldValue));
                }
            }
            
            return objectNode;
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                JsonNode arrayElement = node.get(i);
                ((com.fasterxml.jackson.databind.node.ArrayNode) node).set(i, filterJsonNode(arrayElement));
            }
        }
        
        return node;
    }

    /**
     * 判断字段是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // 精确匹配
        if (SENSITIVE_FIELDS.contains(lowerFieldName)) {
            return true;
        }
        
        // 模式匹配
        for (Pattern pattern : SENSITIVE_PATTERNS) {
            if (pattern.matcher(lowerFieldName).matches()) {
                return true;
            }
        }
        
        return false;
    }
}
