#!/bin/bash

# 请求日志过滤器测试脚本
# 用于测试请求日志过滤器的功能

echo "=== 请求日志过滤器测试 ==="
echo "启动应用程序..."

# 启动应用程序（后台运行）
mvn spring-boot:run -Dspring-boot.run.profiles=local &
APP_PID=$!

# 等待应用程序启动
echo "等待应用程序启动..."
sleep 30

# 检查应用程序是否启动成功
if ! curl -s http://localhost:8080/api/open/v1/test/hello > /dev/null; then
    echo "应用程序启动失败，退出测试"
    kill $APP_PID 2>/dev/null
    exit 1
fi

echo "应用程序启动成功，开始测试..."

# 测试1: 普通API请求
echo ""
echo "测试1: 普通API请求"
curl -X GET "http://localhost:8080/api/open/v1/test/hello" \
     -H "Content-Type: application/json" \
     -H "User-Agent: TestScript/1.0"

# 测试2: 包含敏感数据的POST请求
echo ""
echo "测试2: 包含敏感数据的POST请求"
curl -X POST "http://localhost:8080/api/open/v1/kms/create" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer test-token-12345" \
     -d '{
       "secretName": "test-secret",
       "versionId": "v1",
       "secretData": "sensitive-data",
       "password": "user-password",
       "client_secret": "client-secret-value",
       "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC..."
     }'

# 测试3: OAuth token请求（包含敏感参数）
echo ""
echo "测试3: OAuth token请求"
curl -X POST "http://localhost:8080/oauth/token" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -H "Authorization: Basic dGVzdC1jbGllbnQ6dGVzdC1zZWNyZXQ=" \
     -d "grant_type=client_credentials&client_id=test-client&client_secret=test-secret&scope=read"

# 测试4: 查询参数包含敏感信息
echo ""
echo "测试4: 查询参数包含敏感信息"
curl -X GET "http://localhost:8080/api/open/v1/test/hello?token=sensitive-token&password=user-pwd&normal_param=value" \
     -H "Content-Type: application/json"

echo ""
echo "测试完成！"
echo ""
echo "请查看日志文件："
echo "- 控制台输出"
echo "- ./logs/request-log.log"
echo ""

# 等待一段时间让日志输出
sleep 5

# 显示请求日志文件的最后几行
if [ -f "./logs/request-log.log" ]; then
    echo "=== 请求日志文件内容（最后10行）==="
    tail -10 ./logs/request-log.log
else
    echo "请求日志文件不存在，请检查应用程序配置"
fi

# 停止应用程序
echo ""
echo "停止应用程序..."
kill $APP_PID 2>/dev/null
wait $APP_PID 2>/dev/null

echo "测试结束"
