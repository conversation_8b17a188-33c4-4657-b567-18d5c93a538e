package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.InvalidRequestException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.common.exceptions.UnsupportedGrantTypeException;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.stereotype.Component;

/**
 * 自定义异常转换器，用于将 OAuth2Exception 转换为自定义的错误响应格式
 */
@Slf4j
@Component
public class OAuthExceptionTranslator implements WebResponseExceptionTranslator<OAuth2Exception> {


    @Override
    public ResponseEntity<OAuth2Exception> translate(Exception e) throws Exception {
        
        // 首先确保异常是 OAuth2Exception
        if (!(e instanceof OAuth2Exception)) {
            // 对于非 OAuth2Exception，可以记录日志并让默认机制处理
            log.error("An unexpected exception occurred: {}", e.getMessage(), e);
            // 或者抛出，让更高层的异常处理器处理
            throw e;
        }

        OAuth2Exception oauth2Exception = (OAuth2Exception) e;
        Result customError;

        // 根据不同的 OAuth2Exception 类型和消息内容进行映射
        if (e instanceof InvalidRequestException) {
            // 20001: 必要参数缺失
            customError = Result.fail(20001, "The request is missing a required parameter.");
        } else if (e instanceof InvalidClientException) {
            // 20002: 应用认证失败
            customError = Result.fail(20002, "The client secret is invalid.");
        } else if (e instanceof UnsupportedGrantTypeException) {
            // 20036: grant_type 不支持
            customError = Result.fail(20036, "The refresh token passed is invalid. Please check the value.");
        } else if (e instanceof InvalidGrantException) {
            // InvalidGrantException 包含多种情况，需要根据 message 进一步判断
            if (e.getMessage().contains("Invalid refresh token")) {
                // 20026: refresh_token 无效
                customError = Result.fail(20026, "The refresh token passed is invalid. Please check the value.");
            } else if (e.getMessage().contains("Refresh token expired")) {
                // 20037: refresh_token 已过期
                customError = Result.fail(20037, "The refresh token passed has expired. Please generate a new one.");
            } else if (e.getMessage().toLowerCase().contains("authorization code") || e.getMessage().toLowerCase().contains("refresh token")) {
                 // 20024: refresh_token 与 client_id 不匹配 (这是一个概括性的匹配)
                customError = Result.fail(20024, "The provided authorization code or refresh token does not match the provided client ID.");
            } else {
                 // 其他 InvalidGrantException, 例如密码错误
                 // 这里可以定义一个默认的错误码，或者返回原始的 OAuth2 错误
                return buildResponse(oauth2Exception); // 回退到默认格式
            }
        } else {
            // 对于其他未明确映射的 OAuth2 异常，可以回退到默认的响应格式
            return buildResponse(oauth2Exception);
        }
        
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");

        // 使用原始异常中的 HTTP Status Code，通常是 400 或 401
        return new ResponseEntity(customError, headers, HttpStatus.valueOf(oauth2Exception.getHttpErrorCode()));
    }

    /**
     * 一个辅助方法，用于构建标准的OAuth2Exception响应
     */
    private ResponseEntity<OAuth2Exception> buildResponse(OAuth2Exception e) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");
        return new ResponseEntity<>(e, headers, HttpStatus.valueOf(e.getHttpErrorCode()));
    }
}