package com.caidaocloud.open.auth.service.infrastructure.config.filter;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashSet;

import static org.junit.Assert.*;

/**
 * SensitiveDataFilterConfig 测试类
 * 
 * <AUTHOR>
 * @date 2025/9/11
 */
public class SensitiveDataFilterConfigTest {

    private SensitiveConfig config;

    @Before
    public void setUp() {
        config = new SensitiveConfig();
        
        // 设置测试数据
        config.setMask("***");
        config.setFields(new HashSet<>(Arrays.asList(
            "password", "token", "client_secret", "private_key"
        )));
        // config.setPatternFields(Arrays.asList(
        //     ".*password.*", ".*token.*", ".*secret.*", ".*key.*"
        // ));
        //
        // // 初始化
        // config.init();
    }

    @Test
    public void testIsSensitiveField_ExactMatch() {
        // 测试精确匹配
        assertTrue(config.isSensitiveField("password"));
        assertTrue(config.isSensitiveField("PASSWORD")); // 不区分大小写
        assertTrue(config.isSensitiveField("token"));
        assertTrue(config.isSensitiveField("client_secret"));
        assertTrue(config.isSensitiveField("private_key"));
        
        // 测试非敏感字段
        assertFalse(config.isSensitiveField("username"));
        assertFalse(config.isSensitiveField("email"));
    }

    @Test
    public void testIsSensitiveField_PatternMatch() {
        // 测试模式匹配
        assertTrue(config.isSensitiveField("user_password"));
        assertTrue(config.isSensitiveField("access_token"));
        assertTrue(config.isSensitiveField("client_secret_key"));
        assertTrue(config.isSensitiveField("private_key_data"));
        
        // 测试不匹配的字段
        assertFalse(config.isSensitiveField("username"));
        assertFalse(config.isSensitiveField("email"));
        assertFalse(config.isSensitiveField("data"));
    }

    @Test
    public void testIsSensitiveField_NullValue() {
        // 测试null值
        assertFalse(config.isSensitiveField(null));
    }

    @Test
    public void testGetMask() {
        // 测试获取掩码
        assertEquals("***", config.getMask());
    }
    // //
    // // @Test
    // // public void testGetCompiledPatterns() {
    // //     // 测试获取编译后的模式
    // //     assertNotNull(config.getCompiledPatterns());
    // //     assertEquals(4, config.getCompiledPatterns().size());
    // // }
    // //
    // // @Test
    // // public void testGetLowerCaseExactFields() {
    // //     // 测试获取小写字段集合
    // //     assertNotNull(config.getLowerCaseExactFields());
    // //     assertTrue(config.getLowerCaseExactFields().contains("password"));
    // //     assertTrue(config.getLowerCaseExactFields().contains("token"));
    // //     assertTrue(config.getLowerCaseExactFields().contains("client_secret"));
    // //     assertTrue(config.getLowerCaseExactFields().contains("private_key"));
    // // }
    //
    // @Test
    // public void testInit() {
    //     // 测试初始化方法
    //     SensitiveConfig newConfig = new SensitiveConfig();
    //     newConfig.setFields(new HashSet<>(Arrays.asList("TEST_FIELD")));
    //     newConfig.setPatternFields(Arrays.asList(".*test.*"));
    //
    //     // 初始化前应该为null
    //     assertNull(newConfig.getCompiledPatterns());
    //     assertNull(newConfig.getLowerCaseExactFields());
    //
    //     // 初始化
    //     newConfig.init();
    //
    //     // 初始化后应该不为null
    //     assertNotNull(newConfig.getCompiledPatterns());
    //     assertNotNull(newConfig.getLowerCaseExactFields());
    //     assertEquals(1, newConfig.getCompiledPatterns().size());
    //     assertTrue(newConfig.getLowerCaseExactFields().contains("test_field"));
    // }

    @Test
    public void testCaseInsensitiveMatching() {
        // 测试不区分大小写匹配
        assertTrue(config.isSensitiveField("PASSWORD"));
        assertTrue(config.isSensitiveField("Token"));
        assertTrue(config.isSensitiveField("CLIENT_SECRET"));
        assertTrue(config.isSensitiveField("Private_Key"));
        
        // 模式匹配也应该不区分大小写
        assertTrue(config.isSensitiveField("USER_PASSWORD"));
        assertTrue(config.isSensitiveField("Access_Token"));
    }
}
