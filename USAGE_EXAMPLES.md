# 重构后的请求日志过滤器使用示例

## 1. 基本使用（无需修改现有代码）

重构后的过滤器完全向后兼容，现有代码无需任何修改即可使用：

```java
// 过滤器会自动注册，无需手动配置
// 启动应用程序后，所有HTTP请求都会被自动记录
```

## 2. 自定义敏感字段配置

### 2.1 通过配置文件自定义

创建或修改 `application.yml` 或 `application-request-logging.yml`：

```yaml
request:
  logging:
    sensitive:
      # 自定义掩码字符
      mask: "[FILTERED]"
      
      # 精确匹配的敏感字段（不区分大小写）
      exact-fields:
        - password
        - token
        - client_secret
        - private_key
        - api_key
        - session_id
        - csrf_token
        
      # 正则表达式模式匹配的敏感字段
      pattern-fields:
        - ".*password.*"
        - ".*token.*"
        - ".*secret.*"
        - ".*key.*"
        - ".*auth.*"
        - ".*credential.*"
```

### 2.2 不同环境的配置

```yaml
# application-dev.yml (开发环境)
request:
  logging:
    sensitive:
      mask: "***"
      exact-fields:
        - password
        - token

# application-prod.yml (生产环境)  
request:
  logging:
    sensitive:
      mask: "[REDACTED]"
      exact-fields:
        - password
        - token
        - client_secret
        - private_key
        - api_key
        - access_token
        - refresh_token
```

## 3. 独立使用工具类

### 3.1 使用 RequestInfoExtractor

```java
@Service
public class CustomLoggingService {
    
    public void logCustomRequest(HttpServletRequest request) {
        // 提取基本信息
        LocalDateTime requestTime = LocalDateTime.now();
        Map<String, Object> basicInfo = RequestInfoExtractor.extractBasicInfo(request, requestTime);
        
        // 获取客户端IP
        String clientIp = RequestInfoExtractor.getClientIpAddress(request);
        
        // 获取请求头
        Map<String, String> headers = RequestInfoExtractor.getRequestHeaders(request);
        
        // 判断是否应该跳过
        if (!RequestInfoExtractor.shouldSkipLogging(request)) {
            // 执行自定义日志逻辑
            System.out.println("Custom log: " + basicInfo);
        }
    }
}
```

### 3.2 使用 SensitiveDataFilter

```java
@Service
public class DataProcessingService {
    
    @Autowired
    private SensitiveDataFilter sensitiveDataFilter;
    
    public void processUserData(Map<String, Object> userData) {
        // 过滤敏感数据
        Map<String, Object> filteredData = sensitiveDataFilter.filterSensitiveData(userData);
        
        // 安全地记录或传输数据
        auditLog.info("User data processed: {}", filteredData);
    }
    
    public void processJsonData(String jsonData) {
        // 过滤JSON中的敏感数据
        String filteredJson = sensitiveDataFilter.filterSensitiveJsonData(jsonData);
        
        // 安全地存储或传输
        externalService.sendData(filteredJson);
    }
}
```

## 4. 扩展敏感数据过滤规则

### 4.1 添加自定义过滤逻辑

```java
@Component
public class CustomSensitiveDataFilter extends SensitiveDataFilter {
    
    public CustomSensitiveDataFilter(SensitiveDataFilterConfig config) {
        super(config);
    }
    
    @Override
    public Map<String, Object> filterSensitiveData(Map<String, ?> data) {
        // 先执行基本过滤
        Map<String, Object> filtered = super.filterSensitiveData(data);
        
        // 添加自定义过滤逻辑
        filtered = applyCustomFilters(filtered);
        
        return filtered;
    }
    
    private Map<String, Object> applyCustomFilters(Map<String, Object> data) {
        // 例如：过滤邮箱地址
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (entry.getKey().toLowerCase().contains("email") && entry.getValue() instanceof String) {
                String email = (String) entry.getValue();
                if (email.contains("@")) {
                    // 只显示邮箱的域名部分
                    data.put(entry.getKey(), "***@" + email.substring(email.indexOf("@") + 1));
                }
            }
        }
        return data;
    }
}
```

### 4.2 动态配置敏感字段

```java
@Component
public class DynamicSensitiveFieldManager {
    
    @Autowired
    private SensitiveDataFilterConfig config;
    
    public void addSensitiveField(String fieldName) {
        Set<String> currentFields = new HashSet<>(config.getExactFields());
        currentFields.add(fieldName);
        config.setExactFields(currentFields);
        config.init(); // 重新初始化
    }
    
    public void addSensitivePattern(String pattern) {
        List<String> currentPatterns = new ArrayList<>(config.getPatternFields());
        currentPatterns.add(pattern);
        config.setPatternFields(currentPatterns);
        config.init(); // 重新初始化
    }
}
```

## 5. 测试示例

### 5.1 单元测试

```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class RequestLoggingIntegrationTest {
    
    @Autowired
    private SensitiveDataFilter sensitiveDataFilter;
    
    @Test
    public void testSensitiveDataFiltering() {
        Map<String, Object> testData = new HashMap<>();
        testData.put("username", "testuser");
        testData.put("password", "secret123");
        testData.put("email", "<EMAIL>");
        
        Map<String, Object> filtered = sensitiveDataFilter.filterSensitiveData(testData);
        
        assertEquals("testuser", filtered.get("username"));
        assertEquals("***", filtered.get("password"));
        assertEquals("<EMAIL>", filtered.get("email"));
    }
}
```

### 5.2 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
public class RequestLoggingFilterIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testRequestLoggingWithSensitiveData() {
        // 构造包含敏感数据的请求
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("username", "testuser");
        requestBody.put("password", "secret123");
        requestBody.put("client_secret", "client-secret-value");
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(
            "/api/test", requestBody, String.class);
        
        // 验证响应
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        // 检查日志文件，确保敏感数据被正确过滤
        // 这里可以读取日志文件或使用日志捕获工具验证
    }
}
```

## 6. 监控和调试

### 6.1 启用调试日志

```yaml
logging:
  level:
    com.caidaocloud.open.auth.service.infrastructure.config.filter: DEBUG
    com.caidaocloud.open.auth.service.infrastructure.util: DEBUG
```

### 6.2 性能监控

```java
@Component
public class RequestLoggingMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter requestCounter;
    private final Timer requestTimer;
    
    public RequestLoggingMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.requestCounter = Counter.builder("request.logging.count")
            .description("Number of requests logged")
            .register(meterRegistry);
        this.requestTimer = Timer.builder("request.logging.duration")
            .description("Request logging processing time")
            .register(meterRegistry);
    }
    
    public void recordRequest() {
        requestCounter.increment();
    }
    
    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

## 7. 最佳实践

### 7.1 配置建议
- 在生产环境中使用更严格的敏感字段配置
- 定期审查和更新敏感字段列表
- 使用环境特定的配置文件

### 7.2 性能建议
- 避免在高频接口上记录过多详细信息
- 合理配置日志文件大小和保留策略
- 监控过滤器的性能影响

### 7.3 安全建议
- 确保日志文件的访问权限正确设置
- 定期检查是否有新的敏感字段需要过滤
- 在开发和测试环境中也要注意敏感数据保护

这些示例展示了重构后代码的灵活性和可扩展性，您可以根据具体需求选择合适的使用方式。
