package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.open.auth.service.infrastructure.config.oauth.RefreshTokenJwtAccessTokenConverter;
import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.util.FastjsonUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.jwt.JwtHelper;
import org.springframework.security.jwt.crypto.sign.Signer;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;


/**
 * 阿里云KMS JWT访问令牌转换器
 * 使用阿里云KMS进行JWT签名和验证
 *
 * <AUTHOR> Zhou
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "jwt.signer.type", havingValue = "aliyun-kms")
public class AliyunKmsJwtAccessTokenConverter extends RefreshTokenJwtAccessTokenConverter {

    @Autowired
    private KmsJwtSigner kmsJwtSigner;

    public AliyunKmsJwtAccessTokenConverter(KmsJwtSigner signer){
        super();
        setSigner(signer);
    }

    // @Override
    // protected String encode(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
    //     Map<String, Object> userInfoMap = null;
    //     // accessToken 身份信息为空，转换 accessToken 失败
    //     PreCheck.preCheckArgument(null == accessToken || null == (userInfoMap = accessToken.getAdditionalInformation()),
    //             "accessToken is empty, convert access token err");
    //
    //     Object userid = userInfoMap.get("userid"), belongId = userInfoMap.get("belongId");
    //     // accessToken 中缺少身份信息
    //     PreCheck.preCheckArgument(null == userid || null == belongId, "Identity information is missing in accesstoken");
    //
    //     String tenantId = belongId.toString(), userId = userid.toString();
    //
    //     try {
    //         // 使用阿里云KMS签名服务
    //         Map<String, Object> params = new TokenDto(userId, tenantId, 2).getMap();
    //         String jwt = kmsJwtSigner.sign(params);
    //         log.info("JWT signed using Aliyun KMS, tenantId={}", tenantId);
    //         return jwt;
    //     } catch (Exception e) {
    //         log.error("Failed to encode JWT with Aliyun KMS", e);
    //         throw new RuntimeException("JWT encoding failed", e);
    //     }
    // }


    protected String encode(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        String content;
        try {
            Map<String, ?> map = getAccessTokenConverter().convertAccessToken(accessToken, authentication);
            content = FastjsonUtil.toJson(map);
        }
        catch (Exception e) {
            throw new IllegalStateException("Cannot convert access token to JSON", e);
        }
        log.info("JWT signed using Aliyun KMS, tenantId={}", accessToken.getAdditionalInformation().get("tenantId"));
        return JwtHelper.encode(content, kmsJwtSigner,kmsJwtSigner.headerParams()).getEncoded();
    }

    @Override
    protected Map<String, Object> decode(String jwt) {
        try {
            // 解析JWT获取头部和载荷
            Jws<Claims> jws = Jwts.parser().parseClaimsJws(jwt);
            JwsHeader header = jws.getHeader();
            String payload = jwt.substring(0, jwt.lastIndexOf('.'));
            String signature = jws.getSignature();
            
            // 使用阿里云KMS验证签名
            boolean verified = kmsJwtSigner.verify(header, payload, signature);
            if (!verified) {
                throw new RuntimeException("JWT signature verification failed");
            }
            
            log.debug("JWT decoded and verified successfully using Aliyun KMS");
            return jws.getBody();
        } catch (Exception e) {
            log.error("Failed to decode JWT with Aliyun KMS: {}", e.getMessage());
            throw new RuntimeException("JWT decoding failed", e);
        }
    }

    /**
     * 获取签名器类型
     */
    public String getSignerType() {
        return "aliyun-kms";
    }
}
